package net.iofun.mdis.module.product.service.category;

import java.util.*;
import jakarta.validation.*;
import net.iofun.mdis.module.product.controller.admin.category.vo.*;
import net.iofun.mdis.module.product.dal.dataobject.category.ProductCategoryDO;
import net.iofun.mdis.framework.common.pojo.PageResult;

/**
 * 产品分类 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductCategoryService {

    /**
     * 创建产品分类
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createCategory(@Valid ProductCategorySaveReqVO createReqVO);

    /**
     * 更新产品分类
     *
     * @param updateReqVO 更新信息
     */
    void updateCategory(@Valid ProductCategorySaveReqVO updateReqVO);

    /**
     * 删除产品分类
     *
     * @param id 编号
     */
    void deleteCategory(String id);

    /**
     * 获得产品分类
     *
     * @param id 编号
     * @return 产品分类
     */
    ProductCategoryDO getCategory(String id);

    /**
     * 获得产品分类分页
     *
     * @param pageReqVO 分页查询
     * @return 产品分类分页
     */
    PageResult<ProductCategoryDO> getCategoryPage(ProductCategoryPageReqVO pageReqVO);

    /**
     * 获得产品分类列表
     *
     * @param listReqVO 查询条件
     * @return 产品分类列表
     */
    List<ProductCategoryDO> getCategoryList(ProductCategoryListReqVO listReqVO);

    /**
     * 根据父分类ID获得产品分类列表
     *
     * @param parentId 父分类ID
     * @param name 分类名称
     * @param status 分类状态
     * @return 产品分类列表
     */
    List<ProductCategoryDO> getChildCategoryList(String parentId, Integer status);

}