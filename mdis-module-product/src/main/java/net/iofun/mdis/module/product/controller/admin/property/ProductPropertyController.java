package net.iofun.mdis.module.product.controller.admin.property;

import net.iofun.mdis.module.product.controller.admin.property.vo.property.ProductPropertyPageReqVO;
import net.iofun.mdis.module.product.controller.admin.property.vo.property.ProductPropertyRespVO;
import net.iofun.mdis.module.product.controller.admin.property.vo.property.ProductPropertySaveReqVO;
import net.iofun.mdis.module.product.controller.admin.property.vo.property.ProductPropertySimpleRespVO;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import net.iofun.mdis.framework.common.pojo.PageParam;
import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.common.pojo.CommonResult;
import net.iofun.mdis.framework.common.util.object.BeanUtils;
import static net.iofun.mdis.framework.common.pojo.CommonResult.success;

import net.iofun.mdis.framework.excel.core.util.ExcelUtils;

import net.iofun.mdis.framework.apilog.core.annotation.ApiAccessLog;
import static net.iofun.mdis.framework.apilog.core.enums.OperateTypeEnum.*;

import net.iofun.mdis.module.product.dal.dataobject.property.ProductPropertyDO;
import net.iofun.mdis.module.product.service.property.ProductPropertyService;

@Tag(name = "管理后台 - 产品属性项")
@RestController
@RequestMapping("/product/property")
@Validated
public class ProductPropertyController {

    @Resource
    private ProductPropertyService propertyService;

    @PostMapping("/create")
    @Operation(summary = "创建产品属性项")
    @PreAuthorize("@ss.hasPermission('product:property:create')")
    public CommonResult<String> createProperty(@Valid @RequestBody ProductPropertySaveReqVO createReqVO) {
        return success(propertyService.createProperty(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新产品属性项")
    @PreAuthorize("@ss.hasPermission('product:property:update')")
    public CommonResult<Boolean> updateProperty(@Valid @RequestBody ProductPropertySaveReqVO updateReqVO) {
        propertyService.updateProperty(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除产品属性项")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('product:property:delete')")
    public CommonResult<Boolean> deleteProperty(@RequestParam("id") String id) {
        propertyService.deleteProperty(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得产品属性项")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('product:property:query')")
    public CommonResult<ProductPropertyRespVO> getProperty(@RequestParam("id") String id) {
        ProductPropertyDO property = propertyService.getProperty(id);
        return success(BeanUtils.toBean(property, ProductPropertyRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得产品属性项分页")
    @PreAuthorize("@ss.hasPermission('product:property:query')")
    public CommonResult<PageResult<ProductPropertyRespVO>> getPropertyPage(@Valid ProductPropertyPageReqVO pageReqVO) {
        PageResult<ProductPropertyDO> pageResult = propertyService.getPropertyPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ProductPropertyRespVO.class));
    }

    @GetMapping("/all-simple-list")
    @Operation(summary = "获得产品属性项分页")
    @PreAuthorize("@ss.hasPermission('product:property:query')")
    public CommonResult<List<ProductPropertySimpleRespVO>> getPropertyAllSimpleList() {
        List<ProductPropertyDO> allList = propertyService.getPropertyAllList();
        return success(BeanUtils.toBean(allList, ProductPropertySimpleRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出产品属性项 Excel")
    @PreAuthorize("@ss.hasPermission('product:property:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportPropertyExcel(@Valid ProductPropertyPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ProductPropertyDO> list = propertyService.getPropertyPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "产品属性项.xls", "数据", ProductPropertyRespVO.class,
                        BeanUtils.toBean(list, ProductPropertyRespVO.class));
    }

}