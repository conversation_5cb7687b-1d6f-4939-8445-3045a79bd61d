package net.iofun.mdis.module.product.service.spu;

import java.util.*;
import jakarta.validation.*;
import net.iofun.mdis.module.product.controller.admin.spu.vo.*;
import net.iofun.mdis.module.product.dal.dataobject.spu.ProductSpuDO;
import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.common.pojo.PageParam;

/**
 * 标准产品单元 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductSpuService {

    /**
     * 创建标准产品单元
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createSpu(@Valid ProductSpuSaveReqVO createReqVO);

    /**
     * 更新标准产品单元
     *
     * @param updateReqVO 更新信息
     */
    void updateSpu(@Valid ProductSpuSaveReqVO updateReqVO);

    /**
     * 删除标准产品单元
     *
     * @param id 编号
     */
    void deleteSpu(String id);

    /**
     * 获得标准产品单元
     *
     * @param id 编号
     * @return 标准产品单元
     */
    ProductSpuDO getSpu(String id);

    /**
     * 获得标准产品单元分页
     *
     * @param pageReqVO 分页查询
     * @return 标准产品单元分页
     */
    PageResult<ProductSpuDO> getSpuPage(ProductSpuPageReqVO pageReqVO);

}