package net.iofun.mdis.module.product.service.spu;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import static net.iofun.mdis.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;
import org.bson.types.ObjectId;

import net.iofun.mdis.module.product.controller.admin.computation.vo.ComputationReqVO;
import net.iofun.mdis.module.product.controller.admin.computation.vo.ComputationRespVO;
import net.iofun.mdis.module.product.controller.admin.computation.vo.ComputationVarVO;
import net.iofun.mdis.module.product.controller.admin.spu.vo.*;
import net.iofun.mdis.module.product.dal.dataobject.spu.ProductSpuDO;
import net.iofun.mdis.module.product.dal.dataobject.spu.inner.SpuPropertyInfo;
import net.iofun.mdis.module.product.dal.dataobject.property.ProductPropertyValueDO;
import net.iofun.mdis.module.product.dal.dataobject.property.inner.ExpressionType;
import net.iofun.mdis.module.product.dal.dataobject.property.inner.ExpressionVar;
import net.iofun.mdis.framework.common.enums.ProductPropertyValueTypeEnum;
import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.common.util.object.BeanUtils;

import net.iofun.mdis.module.product.dal.repository.spu.ProductSpuRepository;
import net.iofun.mdis.module.product.service.computation.ComputationService;
import net.iofun.mdis.module.product.service.property.ProductPropertyValueService;

import static net.iofun.mdis.framework.common.exception.util.ServiceExceptionUtil.exception;
import static net.iofun.mdis.module.product.enums.ErrorCodeConstants.*;

import java.util.List;
import java.util.Set;
import java.util.Map;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Deque;
import java.util.ArrayDeque;
import java.util.stream.Collectors;

/**
 * 标准产品单元 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProductSpuServiceImpl implements ProductSpuService {

    @Resource
    private ProductSpuRepository spuRepository;

    @Resource
    private ProductPropertyValueService propertyValueService;

    @Resource
    private ComputationService computationService;

    @Override
    public String createSpu(ProductSpuSaveReqVO createReqVO) {

        // 插入
        ProductSpuDO spu = BeanUtils.toBean(createReqVO, ProductSpuDO.class);
        spuRepository.insert(spu, getLoginUser());
        // 返回
        return spu.getId().toHexString();
    }

    @Override
    public void updateSpu(ProductSpuSaveReqVO updateReqVO) {
        // 校验存在
        validateSpuExists(updateReqVO.getId());
        // 校验属性列表
        validateSpuProperties(updateReqVO.getProperties());
        // 更新
        ProductSpuDO updateObj = BeanUtils.toBean(updateReqVO, ProductSpuDO.class);
        spuRepository.updateById(updateObj, getLoginUser());
    }

    @Override
    public void deleteSpu(String id) {
        ObjectId oid = new ObjectId(id);
        // 校验存在
        validateSpuExists(oid);
        // 删除
        spuRepository.deleteById(oid, getLoginUser());
    }

    private void validateSpuExists(ObjectId id) {
        if (!spuRepository.validateExists(id)) {
            throw exception(SPU_NOT_EXISTS);
        }
    }

    private void validateSpuProperties(List<SpuPropertyInfo> properties) {

        // 获取 number, computed 类型的 valueId 和 计算结果
        Map<String, BigDecimal> propertiesMap = properties.stream()
        .filter(item -> !item.getValueType().equals(ProductPropertyValueTypeEnum.ENUMS.getValue()))
        .collect(Collectors.toMap(SpuPropertyInfo::getValueId, SpuPropertyInfo::getEvaluatedValue));

        // 获取 properties 中, computed 类型的元素, 并且按照 valuedId 被引用的关系, 进行排序
        List<SpuPropertyInfo> computedProperties = properties.stream()
        .filter(item -> item.getValueType().equals(ProductPropertyValueTypeEnum.COMPUTED.getValue()))
        .collect(Collectors.toList());

        // 遍历 computedProperties, 获取每个计算类型的属性值详情, 只要有不存在, 则抛出异常
        Map<String, ExpressionType> computedPropertValueExprMap = new HashMap<>();
        Map<String, Set<String>> relationIdsMap = new HashMap<>();
        computedProperties.forEach(item -> {
            // 获取这个计算类型的属性值
            ProductPropertyValueDO propertyValue = propertyValueService.getPropertyValue(item.getValueId());
            if (propertyValue == null) {
                throw exception(PROPERTY_VALUE_NOT_EXISTS);
            } else {
                computedPropertValueExprMap.put(item.getValueId(), propertyValue.getValueExpr());
                relationIdsMap.put(item.getValueId(), propertyValue.getValueExpr().getVars().stream()
                .filter(var -> var.getValueId() != null)
                .map(var -> var.getValueId())
                .collect(Collectors.toSet()));
            }
        });

        // 根据 relationIdsMap 之间的 valueId 的引用关系, 生成 properties 中 computed 类型的元素的 valueId 的计算顺序列表
        List<String> computedSequenceIds = genComputedSequence(relationIdsMap);
        
        // 检验 & 计算: 按排序后的 computed 列表逐一校验依赖并验证计算结果
        for (String spuPropertyValueId : computedSequenceIds) {
        
            // 构建计算请求
            ComputationReqVO computationReqVO = new ComputationReqVO();
            List<ComputationVarVO> exprVars = new ArrayList<>();

            ExpressionType exprType = computedPropertValueExprMap.get(spuPropertyValueId);
            if (exprType == null) {
                throw exception(EXPR_COMPUTATION_FAILED, "计算属性缺少表达式: " + spuPropertyValueId);
            }

            // 1. 校验依赖是否存在，并准备变量
            if (exprType.getVars() != null) {
                for (ExpressionVar var : exprType.getVars()) {
                    if (var == null || var.getValueId() == null) continue;
                    if (propertiesMap.containsKey(var.getValueId())) {
                        exprVars.add(ComputationVarVO.builder()
                                .label(var.getLabel())
                                .valueId(var.getValueId())
                                .value(propertiesMap.get(var.getValueId()).toString())
                                .build());
                    } else {
                        throw exception(EXPR_VARS_VALUE_NOT_EXISTS);
                    }
                }
            }

            // 2. 校验计算结果是否正确
            try {
                computationReqVO.setExpr(exprType.getExpr());
                computationReqVO.setVars(exprVars);
                ComputationRespVO newResult = computationService.computeByValue(computationReqVO);
                if (newResult.getSuccess()) {
                    BigDecimal result = new BigDecimal(newResult.getResult());
                    if (propertiesMap.get(spuPropertyValueId).compareTo(result) != 0) {
                        throw exception(EXPR_COMPUTATION_FAILED, "计算结果不正确");
                    }
                    // 更新到 propertiesMap，供后续依赖使用
                    propertiesMap.put(spuPropertyValueId, result);
                } else {
                    throw exception(EXPR_COMPUTATION_FAILED, newResult.getErrorMessage());
                }
            } catch (Exception e) {
                throw exception(EXPR_COMPUTATION_FAILED, e.getMessage());
            }
        }
    }

    @Override
    public ProductSpuDO getSpu(String id) {
        return spuRepository.selectById(id);
    }

    @Override
    public PageResult<ProductSpuDO> getSpuPage(ProductSpuPageReqVO pageReqVO) {
        return spuRepository.selectPage(pageReqVO);
    }

    /* 遍历 computedPropertValueExprMap, 按照 valueId 被引用的关系, 进行计算顺序的排序, 排序规则:
            1. 没有被引用的最后计算(都没有被引用的,无所谓先后顺序);
            2. 被引用,但是没有引用其他, 则首先进行计算(都是被引用但没有引用其他的, 则无所谓先后顺序);
            3, 被引用, 同时也引用了其他的, 则要等被引用的先计算完, 才能计算引用者;
            4. 不能存在自己引用自己, 也不能存在循环引用, 如果发现就抛出异常;
            computedPropertValueExprMap 的数据结构, computedPropertValueExprMap的 key 是 valueId, value 是 List<ExpressionVar> 类型, ExpressionVar 的 valueId 是表示被引用的 valueId;
            如果  computedPropertValueExprMap 的A记录的 key 这个 valueId, 等于在自己这条A记录的 value(List<ExpressionVar>) 的某个元素的 valueId 中, 就是自己引用自己, 抛出异常;
            如果  computedPropertValueExprMap 的A记录的 key 这个 valueId, 等于在B记录的 value(List<ExpressionVar>) 的某个元素的 valueId 中, 就是 B 记录引用了 A 记录的 key 这个 valueId, 则需要等A这条记录计算完成后, 才能计算B记录;
            如果, computedPropertValueExprMap 的A记录的 key 这个 valueId, 被B记录的 value(List<ExpressionVar>) 的某个元素引用, 而 B记录的 key这个 valueId, 被C记录的 value(List<ExpressionVar>) 的某个元素引用, 则需要等A这条记录计算完成后, 才能计算B记录, 再计算C记录; 
            如果, computedPropertValueExprMap 的A记录的 key 这个 valueId, 被B记录的 value(List<ExpressionVar>) 的某个元素引用, 而 B记录的 key这个 valueId, 被A记录的 value(List<ExpressionVar>) 的某个元素引用, 这就是循环引用, 抛出异常;
            如果, computedPropertValueExprMap 的A记录的 key 这个 valueId, 被B记录的 value(List<ExpressionVar>) 的某个元素引用, 而 B记录的 key这个 valueId, 被C记录的 value(List<ExpressionVar>) 的某个元素引用, 而 C 记录的 key 这个 valueId, 被A记录的 value(List<ExpressionVar>) 的某个元素引用, 这也是循环引用, 抛出异常;
        */
    private List<String> genComputedSequence(Map<String, Set<String>> relationIdsMap) {
            // 计算型属性的 valueId 集合
            Set<String> computedIds = relationIdsMap.keySet();

            // 邻接表: 先决 -> 依赖者（A 被 B 引用，边 A->B）
            Map<String, Set<String>> adjacency = new HashMap<>();
            // 入度: 被依赖的数量（先决条件数量）
            Map<String, Integer> indegree = new HashMap<>();
            // 出度: 被多少个引用（被作为他人先决）
            Map<String, Integer> outdegree = new HashMap<>();

            computedIds.forEach(id -> {
                indegree.put(id, 0);
                outdegree.put(id, 0);
                adjacency.put(id, new HashSet<>());
            });

            // 构建图，同时检测自引用
            for (String targetId : computedIds) {
                for (String depId : relationIdsMap.get(targetId)) {
                    // 只关心依赖到其他计算型属性的引用
                    if (!computedIds.contains(depId)) {
                        continue;
                    }
                    // 自引用检测
                    if (depId.equals(targetId)) {
                        throw exception(EXPR_COMPUTATION_FAILED, "表达式存在自引用: " + targetId);
                    }
                    // 如果 depId 在 computedIds 中, 且构建的边: depId -> targetId, 是第一次构建, 则更新 indegree 和 outdegree
                    if (computedIds.contains(depId) && adjacency.get(depId).add(targetId)) {
                        indegree.put(targetId, indegree.get(targetId) + 1);
                        outdegree.put(depId, outdegree.get(depId) + 1);
                    }
                }
            }

            // Kahn 拓扑排序，优先处理「被引用但不引用他人」的节点
            Deque<String> primaryQueue = new ArrayDeque<>();   // 入度为 0，且出度 > 0, 这些节点没有依赖项, 优先级高，会优先处理
            Deque<String> secondaryQueue = new ArrayDeque<>(); // 入度为 0，且出度 = 0（无人引用）, 优先级低，会最后处理

            for (String id : computedIds) {
                if (indegree.get(id) == 0) {
                    if (outdegree.get(id) > 0) primaryQueue.add(id); else secondaryQueue.add(id);
                }
            }

            List<String> topoOrder = new ArrayList<>();
            while (!primaryQueue.isEmpty() || !secondaryQueue.isEmpty()) { // 只要两个队列中任一不为空，就继续循环
                // 优先从主队列取节点，如果主队列空了才从次队列取; pollFirst()：取出并移除队列头部元素
                String cur = !primaryQueue.isEmpty() ? primaryQueue.pollFirst() : secondaryQueue.pollFirst();
                topoOrder.add(cur);
                // 遍历当前节点的所有依赖节点, getOrDefault(cur, new HashSet<>()), 获取cur的邻接节点，如果不存在返回空集合
                for (String next : adjacency.getOrDefault(cur, new HashSet<>())) {
                    // 将依赖节点的入度减1（因为它的一个先决条件cur已经处理完了）
                    indegree.put(next, indegree.get(next) - 1);
                    if (indegree.get(next) == 0) { // 如果依赖节点的入度变为0（所有先决条件都满足了）
                        // 根据它的出度决定放入哪个队列, 出度 > 0 放入主队列, 出度 = 0 放入次队列
                        if (outdegree.get(next) > 0) primaryQueue.add(next); else secondaryQueue.add(next);
                    }
                }
            }

            // 若未处理完所有节点，则存在循环引用
            if (topoOrder.size() < computedIds.size()) {
                throw exception(EXPR_COMPUTATION_FAILED, "表达式存在循环引用");
            }

            return topoOrder;
    }

}