package net.iofun.mdis.module.product.dal.dataobject.spu;

import lombok.*;
import java.util.*;
import net.iofun.mdis.framework.common.entity.BaseEntity;
import net.iofun.mdis.module.product.dal.dataobject.spu.inner.SpuComponentInfo;
import net.iofun.mdis.module.product.dal.dataobject.spu.inner.SpuPropertyInfo;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * 标准产品单元 DO
 *
 * <AUTHOR>
 */
@Document("product_spu")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductSpuDO extends BaseEntity {

    /**
     * 名称
     */
    private String name;
    /**
     * 英文名称
     */
    @Field("label_en")
    private String labelEn;
    /**
     * 分类ID
     */
    @Field("category_id")
    private String categoryId;
    /**
     * 类型, "10": 配件单元, "20": 产品单元
     */
    private String type;
    /**
     * 单位ID
     */
    @Field("unit_id")
    private String unitId;
    /**
     * 基础属性
     */
    private List<SpuPropertyInfo> properties;
    /**
     * 配件表
     */
    private List<SpuComponentInfo> components;
    /**
     * 图片组
     */
    @Field("pic_urls")
    private List<String> picUrls;
    /**
     * 开启状态
     *
     * 枚举 {@link TODO common_status 对应的类}
     */
    private Integer status;
    /**
     * 编码
     */
    private String code;

}