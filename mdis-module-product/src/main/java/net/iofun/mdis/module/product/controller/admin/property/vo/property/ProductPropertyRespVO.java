package net.iofun.mdis.module.product.controller.admin.property.vo.property;

import io.swagger.v3.oas.annotations.media.Schema;
import net.iofun.mdis.framework.common.vo.BaseVO;
import lombok.*;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 产品属性项 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProductPropertyRespVO extends BaseVO {

    @Schema(description = "属性名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("属性名称")
    private String name;

    @Schema(description = "英文名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("英文名称")
    private String labelEn;

    @Schema(description = "分类级别", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("分类级别")
    private Integer cateLevel;

    @Schema(description = "属性类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("属性类型")
    private String attrType;

    @Schema(description = "属性值类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("属性值类型")
    private String valueType;

    @Schema(description = "是否显示", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否显示")
    private Boolean isDisplay;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("排序")
    private Integer sort;

}