package net.iofun.mdis.module.product.controller.admin.category.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import net.iofun.mdis.framework.common.vo.BaseVO;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 产品分类新增/修改 Request VO")
@Data
public class ProductCategorySaveReqVO extends BaseVO {

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "名称不能为空")
    private String name;

    @Schema(description = "父类ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "父类ID不能为空")
    private String parentId;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "排序不能为空")
    private Integer sort;

    @Schema(description = "开启状态", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "开启状态不能为空")
    private Integer status;

    @Schema(description = "分类图地址")
    private String picUrl;

    @Schema(description = "分类层级", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "分类层级不能为空")
    private Integer level;

}