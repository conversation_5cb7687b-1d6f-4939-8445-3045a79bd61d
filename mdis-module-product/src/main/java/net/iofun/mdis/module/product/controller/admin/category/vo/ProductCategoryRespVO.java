package net.iofun.mdis.module.product.controller.admin.category.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import net.iofun.mdis.framework.common.vo.BaseVO;
import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import com.alibaba.excel.annotation.*;
import net.iofun.mdis.framework.excel.core.annotations.DictFormat;
import net.iofun.mdis.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 产品分类 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProductCategoryRespVO extends BaseVO {

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("名称")
    private String name;

    @Schema(description = "父类ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("父类ID")
    private String parentId;

    @Schema(description = "显示顺序", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("显示顺序")
    private Integer sort;

    @Schema(description = "开启状态", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "开启状态", converter = DictConvert.class)
    @DictFormat("common_status")
    private Integer status;

    @Schema(description = "分类层级", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("分类层级")
    private Integer level;

    @Schema(description = "分类图标")
    @ExcelProperty("分类图标")
    private String picUrl;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

}