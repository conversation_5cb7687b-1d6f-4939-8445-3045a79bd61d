package net.iofun.mdis.module.product.controller.admin.computation.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import java.util.List;

/**
 * 计算请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "计算请求")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ComputationReqVO {

    @Schema(description = "表达式字符串", required = true, example = "a + b * c")
    @NotEmpty(message = "表达式不能为空")
    private String expr;

    @Schema(description = "变量列表", required = true)
    @NotEmpty(message = "变量列表不能为空")
    @Valid
    private List<ComputationVarVO> vars;
}