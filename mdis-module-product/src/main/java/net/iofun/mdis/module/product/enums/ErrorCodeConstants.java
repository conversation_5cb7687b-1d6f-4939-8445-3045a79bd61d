package net.iofun.mdis.module.product.enums;

import net.iofun.mdis.framework.common.exception.ErrorCode;

/**
 * Product 模块 错误码枚举类
 *
 * 使用 1-003-000-000 段
 * <AUTHOR>
 */

public interface ErrorCodeConstants {
    // ========== 产品分类 1_003_001_000 段 ==========
    ErrorCode CATEGORY_NOT_EXISTS = new ErrorCode(1_003_001_001, "产品分类不存在");
    ErrorCode CATEGORY_PARENT_NOT_EXISTS = new ErrorCode(1_003_001_002, "产品父分类不存在");
    ErrorCode CATEGORY_PARENT_NOT_LAST_LEVEL = new ErrorCode(1_003_001_003, "产品父分类不能是末级分类");

    // ========== 计量单位 1_003_002_000 ==========
    ErrorCode UNIT_NOT_EXISTS = new ErrorCode(1_003_002_001, "计量单位不存在");

    // ========== 产品属性 1_003_003_000 ==========
    ErrorCode PROPERTY_NOT_EXISTS = new ErrorCode(1_003_003_001, "产品属性项不存在");
    ErrorCode PROPERTY_VALUE_NOT_EXISTS = new ErrorCode(1_003_003_002, "产品属性值不存在");
    ErrorCode PROPERTY_NAME_LABEL_EN_DUPLICATED = new ErrorCode(1_003_003_003, "产品属性项名词或者英文名称重复");
    ErrorCode PROPERTY_CATE_LEVEL_INVALID = new ErrorCode(1_003_003_004, "产品属性项分类级别必须是3或4级");
    ErrorCode PROPERTY_VALUE_ENUMS_TYPE_NOT_MATCH = new ErrorCode(1_003_003_005, "产品属性值文字枚举类型不匹配");
    ErrorCode PROPERTY_VALUE_RANGE_TYPE_NOT_MATCH = new ErrorCode(1_003_003_006, "产品属性值数值范围类型不匹配");
    ErrorCode PROPERTY_VALUE_COMPUTED_TYPE_NOT_MATCH = new ErrorCode(1_003_003_007, "产品属性值计算公式类型不匹配");
    ErrorCode PROPERTY_VALUE_TYPE_NOT_MATCH = new ErrorCode(1_003_003_008, "产品属性值任何类型不匹配");
    ErrorCode PROPERTY_VALUE_ONLY_ENUMS_TYPE_UNIT_ZERO = new ErrorCode(1_003_003_009, "产品属性值只有在文字枚举类型时,计量单位才是'0'");
    ErrorCode PROPERTY_VALUE_RANGE_INVALID = new ErrorCode(1_003_003_010, "产品属性值数值范围不合法");
    ErrorCode PROPERTY_VALUE_EXPR_VARS_NOT_EXISTS = new ErrorCode(1_003_003_011, "产品属性值表达式变量不存在");

    // ========== 标准产品单元 1_003_004_000 ==========
    ErrorCode SPU_NOT_EXISTS = new ErrorCode(1_003_004_001, "标准产品单元不存在");

    // ========== 产品计算 1_003_005_000 ==========
    ErrorCode EXPR_VALIDATION_FAILED = new ErrorCode(1_003_005_001, "表达式验证失败");
    ErrorCode EXPR_COMPUTATION_FAILED = new ErrorCode(1_003_005_002, "表达式计算失败");
    ErrorCode EXPR_VARS_VALUE_NOT_EXISTS = new ErrorCode(1_003_005_003, "计算公式依赖的属性值不存在");
}

