package net.iofun.mdis.module.product.controller.admin.property.vo.property;

import io.swagger.v3.oas.annotations.media.Schema;
import net.iofun.mdis.framework.common.vo.BaseVO;
import lombok.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 产品属性项新增/修改 Request VO")
@Data
public class ProductPropertySaveReqVO extends BaseVO {

    @Schema(description = "属性名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "属性名称不能为空")
    private String name;

    @Schema(description = "英文名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "英文名称不能为空")
    private String labelEn;

    @Schema(description = "分类级别", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "分类级别不能为空")
    private Integer cateLevel;

    @Schema(description = "属性类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "属性类型不能为空")
    private String attrType;

    @Schema(description = "属性值类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "属性值类型不能为空")
    private String valueType;

    @Schema(description = "是否显示", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否显示不能为空")
    private Boolean isDisplay;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "排序不能为空")
    private Integer sort;

}