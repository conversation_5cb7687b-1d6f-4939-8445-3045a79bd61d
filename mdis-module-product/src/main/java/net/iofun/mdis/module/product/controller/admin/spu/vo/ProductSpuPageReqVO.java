package net.iofun.mdis.module.product.controller.admin.spu.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import net.iofun.mdis.framework.common.pojo.PageParam;

@Schema(description = "管理后台 - 标准产品单元分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductSpuPageReqVO extends PageParam {

    @Schema(description = "名称")
    private String name;

    @Schema(description = "英文名称")
    private String labelEn;

    @Schema(description = "分类ID")
    private String categoryId;

    @Schema(description = "类型")
    private String type;

    @Schema(description = "开启状态")
    private Integer status;

    @Schema(description = "编码")
    private String code;

}