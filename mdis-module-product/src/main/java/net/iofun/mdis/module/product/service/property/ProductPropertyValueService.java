package net.iofun.mdis.module.product.service.property;

import java.util.List;

import jakarta.validation.*;
import net.iofun.mdis.module.product.controller.admin.property.vo.value.ProductPropertyValueListReqVO;
import net.iofun.mdis.module.product.controller.admin.property.vo.value.ProductPropertyValuePageReqVO;
import net.iofun.mdis.module.product.controller.admin.property.vo.value.ProductPropertyValueSaveReqVO;
import net.iofun.mdis.module.product.controller.admin.property.vo.value.ProductPropertyValueSimpleRespVO;
import net.iofun.mdis.module.product.dal.dataobject.property.ProductPropertyValueDO;
import net.iofun.mdis.framework.common.pojo.PageResult;

/**
 * 产品属性值 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductPropertyValueService {

    /**
     * 创建产品属性值
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPropertyValue(@Valid ProductPropertyValueSaveReqVO createReqVO);

    /**
     * 更新产品属性值
     *
     * @param updateReqVO 更新信息
     */
    void updatePropertyValue(@Valid ProductPropertyValueSaveReqVO updateReqVO);

    /**
     * 删除产品属性值
     *
     * @param id 编号
     */
    void deletePropertyValue(String id);

    /**
     * 获得产品属性值
     *
     * @param id 编号
     * @return 产品属性值
     */
    ProductPropertyValueDO getPropertyValue(String id);

    /**
     * 获得产品属性值分页
     *
     * @param pageReqVO 分页查询
     * @return 产品属性值分页
     */
    PageResult<ProductPropertyValueDO> getPropertyValuePage(ProductPropertyValuePageReqVO pageReqVO);

    /**
     * 获得产品属性值列表
     *
     * @param listReqVO 列表查询
     * @return 产品属性值列表
     */
    List<ProductPropertyValueSimpleRespVO> getPropertyValueSimpleList(ProductPropertyValueListReqVO listReqVO);

}