package net.iofun.mdis.module.product.service.property;

import jakarta.validation.*;
import net.iofun.mdis.module.product.controller.admin.property.vo.property.ProductPropertyPageReqVO;
import net.iofun.mdis.module.product.controller.admin.property.vo.property.ProductPropertySaveReqVO;
import net.iofun.mdis.module.product.dal.dataobject.property.ProductPropertyDO;
import net.iofun.mdis.framework.common.pojo.PageResult;

import java.util.List;

/**
 * 产品属性项 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductPropertyService {

    /**
     * 创建产品属性项
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createProperty(@Valid ProductPropertySaveReqVO createReqVO);

    /**
     * 更新产品属性项
     *
     * @param updateReqVO 更新信息
     */
    void updateProperty(@Valid ProductPropertySaveReqVO updateReqVO);

    /**
     * 删除产品属性项
     *
     * @param id 编号
     */
    void deleteProperty(String id);

    /**
     * 获得产品属性项
     *
     * @param id 编号
     * @return 产品属性项
     */
    ProductPropertyDO getProperty(String id);

    /**
     * 获得产品属性项分页
     *
     * @param pageReqVO 分页查询
     * @return 产品属性项分页
     */
    PageResult<ProductPropertyDO> getPropertyPage(ProductPropertyPageReqVO pageReqVO);

    /**
     * @return 返回全部产品属性项列表
     */
    List<ProductPropertyDO> getPropertyAllList();

}