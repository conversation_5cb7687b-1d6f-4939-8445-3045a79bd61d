package net.iofun.mdis.module.product.controller.admin.computation;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import net.iofun.mdis.framework.common.pojo.CommonResult;
import net.iofun.mdis.module.product.controller.admin.computation.vo.ComputationReqVO;
import net.iofun.mdis.module.product.controller.admin.computation.vo.ComputationRespVO;
import net.iofun.mdis.module.product.service.computation.ComputationService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import jakarta.annotation.Resource;

/**
 * 计算控制器
 *
 * <AUTHOR>
 */
@Tag(name = "产品计算", description = "产品相关表达式计算")
@RestController
@RequestMapping("/product/computation")
@Validated
public class ComputationController {

    @Resource
    private ComputationService computationService;

    @PostMapping("/compute-by-value")
    @Operation(summary = "根据变量值计算", description = "前端传来的变量都有明确的值，直接进行表达式计算")
    public CommonResult<ComputationRespVO> computeDirectly(@Valid @RequestBody ComputationReqVO reqVO) {
        ComputationRespVO result = computationService.computeDirectly(reqVO);
        return CommonResult.success(result);
    }

    @PostMapping("/compute-by-refer")
    @Operation(summary = "根据变量值引用解析后再计算")
    public CommonResult<ComputationRespVO> computeWithVariableResolution(@Valid @RequestBody ComputationReqVO reqVO) {
        ComputationRespVO result = computationService.computeWithVariableResolution(reqVO);
        return CommonResult.success(result);
    }
}