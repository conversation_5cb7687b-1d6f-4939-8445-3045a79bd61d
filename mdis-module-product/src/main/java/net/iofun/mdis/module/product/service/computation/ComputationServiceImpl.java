package net.iofun.mdis.module.product.service.computation;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.iofun.mdis.framework.aviatorscript.core.ExpressionCalculator;
import net.iofun.mdis.framework.aviatorscript.model.ComputationResult;
import net.iofun.mdis.framework.aviatorscript.model.ExpressionRequest;
import net.iofun.mdis.framework.aviatorscript.model.ExpressionVariable;
import net.iofun.mdis.module.product.controller.admin.computation.vo.ComputationReqVO;
import net.iofun.mdis.module.product.controller.admin.computation.vo.ComputationRespVO;
import net.iofun.mdis.module.product.controller.admin.computation.vo.ComputationVarVO;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 计算服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ComputationServiceImpl implements ComputationService {

    @Resource
    private ExpressionCalculator expressionCalculator;

    @Override
    public Boolean validateExpression(ComputationReqVO request) {
        return expressionCalculator.validateExpression(request.getExpr());
        
    }

    @Override
    public ComputationRespVO computeByValue(ComputationReqVO request) {
        log.debug("开始场景一计算：直接计算，表达式：{}", request.getExpr());
        
        try {
            // 转换 VO 为框架模型
            ExpressionRequest expressionRequest = convertToExpressionRequest(request, false);
            
            // 调用计算引擎
            ComputationResult result = expressionCalculator.calculate(expressionRequest);
            
            // 转换结果
            return convertToComputationRespVO(result);
            
        } catch (Exception e) {
            log.error("场景一计算失败", e);
            return ComputationRespVO.failure("计算失败: " + e.getMessage());
        }
    }

    @Override
    public ComputationRespVO computeByReference(ComputationReqVO request) {
        log.debug("开始场景二计算：补全变量后计算，表达式：{}", request.getExpr());
        
        try {
            // 转换 VO 为框架模型，启用变量解析
            ExpressionRequest expressionRequest = convertToExpressionRequest(request, true);
            
            // 调用计算引擎
            ComputationResult result = expressionCalculator.calculate(expressionRequest);
            
            // 转换结果
            return convertToComputationRespVO(result);
            
        } catch (Exception e) {
            log.error("场景二计算失败", e);
            return ComputationRespVO.failure("计算失败: " + e.getMessage());
        }
    }

    /**
     * 转换 VO 为框架模型
     */
    private ExpressionRequest convertToExpressionRequest(ComputationReqVO request, boolean resolveVariables) {
        List<ExpressionVariable> variables = new ArrayList<>();
        
        for (ComputationVarVO varVO : request.getVars()) {
            BigDecimal value = null;
            
            // 如果有直接的值，则转换
            if (StringUtils.hasText(varVO.getValue())) {
                try {
                    value = new BigDecimal(varVO.getValue());
                } catch (NumberFormatException e) {
                    log.warn("无法解析变量值：{}", varVO.getValue(), e);
                    continue;
                }
            }
            // 如果需要解析变量且没有直接值
            else if (resolveVariables && value == null) {
                value = resolveVariableValue(varVO);
            }
            
            // 只有当变量有值时才添加到计算环境中
            if (value != null) {
                variables.add(ExpressionVariable.builder()
                        .name(varVO.getLabel())
                        .value(value)
                        .build());
            }
        }
        
        return ExpressionRequest.builder()
                .expression(request.getExpr())
                .variables(variables)
                .build();
    }

    /**
     * 解析变量值
     * TODO: 根据具体业务逻辑实现变量解析
     */
    private BigDecimal resolveVariableValue(ComputationVarVO varVO) {
        // 这里是场景二的核心逻辑，需要根据具体业务需求实现
        // 可能需要根据 valueId, spuId, skuId, categoryId 等从数据库查询对应的值
        
        log.debug("开始解析变量：label={}, valueId={}, spuId={}, skuId={}, categoryId={}", 
                varVO.getLabel(), varVO.getValueId(), varVO.getSpuId(), varVO.getSkuId(), varVO.getCategoryId());
        
        // TODO: 实现具体的变量解析逻辑
        // 例如：
        // 1. 如果有 valueId，从 ProductPropertyValue 中查询值
        // 2. 如果有 spuId，从 ProductSpu 中查询相关属性值
        // 3. 如果有 categoryId，从分类相关的配置中查询默认值
        // 4. 可能需要组合多个条件进行查询
        
        log.warn("变量解析逻辑尚未实现，变量：{}", varVO.getLabel());
        return null;
    }

    /**
     * 转换计算结果为响应 VO
     */
    private ComputationRespVO convertToComputationRespVO(ComputationResult result) {
        if (result.getSuccess()) {
            // 将 BigDecimal 转换为字符串以保持精度
            String resultValue = result.getResult() != null ? result.getResult().toPlainString() : "0";
            return ComputationRespVO.success(resultValue);
        } else {
            return ComputationRespVO.failure(result.getErrorMessage());
        }
    }
}