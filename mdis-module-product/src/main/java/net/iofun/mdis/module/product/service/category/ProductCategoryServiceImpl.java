package net.iofun.mdis.module.product.service.category;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import cn.hutool.core.collection.CollUtil;

import java.util.*;
import java.util.stream.Collectors;

import static net.iofun.mdis.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;
import net.iofun.mdis.module.product.service.property.ProductPropertyValueServiceImpl;
import org.bson.types.ObjectId;
import net.iofun.mdis.module.product.controller.admin.category.vo.*;
import net.iofun.mdis.module.product.dal.dataobject.category.ProductCategoryDO;
import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.common.util.object.BeanUtils;
import static net.iofun.mdis.framework.common.util.collection.CollectionUtils.convert2Set;
import net.iofun.mdis.module.product.dal.repository.category.ProductCategoryRepository;

import static net.iofun.mdis.framework.common.exception.util.ServiceExceptionUtil.exception;
import static net.iofun.mdis.module.product.dal.dataobject.category.ProductCategoryDO.CATEGORY_LEVEL;
import static net.iofun.mdis.module.product.dal.dataobject.category.ProductCategoryDO.PARENT_ID_NULL;
import static net.iofun.mdis.module.product.enums.ErrorCodeConstants.*;

/**
 * 产品分类 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProductCategoryServiceImpl implements ProductCategoryService {

    @Resource
    private ProductCategoryRepository categoryRepository;

    @Override
    public String createCategory(ProductCategorySaveReqVO createReqVO) {
        // 校验父分类存在
        validateParentProductCategory(createReqVO.getParentId());
        // 插入
        ProductCategoryDO category = BeanUtils.toBean(createReqVO, ProductCategoryDO.class);
        categoryRepository.insert(category, getLoginUser());
        // 返回
        return category.getId().toHexString();
    }

    @Override
    public void updateCategory(ProductCategorySaveReqVO updateReqVO) {
        // 校验存在
        validateCategoryExists(updateReqVO.getId());
        // 更新
        ProductCategoryDO updateObj = BeanUtils.toBean(updateReqVO, ProductCategoryDO.class);
        categoryRepository.updateById(updateObj, getLoginUser());
    }

    @Override
    public void deleteCategory(String id) {
        ObjectId oid = new ObjectId(id);
        // 校验存在
        validateCategoryExists(oid);
        // 删除
        categoryRepository.deleteById(oid, getLoginUser());
    }

    private void validateCategoryExists(ObjectId id) {
        if (!categoryRepository.validateExists(id)) {
            throw exception(CATEGORY_NOT_EXISTS);
        }
    }

    @Override
    public ProductCategoryDO getCategory(String id) {
        return categoryRepository.selectById(id);
    }

    @Override
    public PageResult<ProductCategoryDO> getCategoryPage(ProductCategoryPageReqVO pageReqVO) {
        return categoryRepository.selectPage(pageReqVO);
    }

    @Override
    public List<ProductCategoryDO> getCategoryList(ProductCategoryListReqVO listReqVO) {
        List<ProductCategoryDO> list = categoryRepository.selectList(listReqVO);
        return list;
    }

    private void validateParentProductCategory(String parentId) {
        // 如果是根分类，无需验证
        if (parentId.equals(PARENT_ID_NULL)) {
            return;
        }
        // 父分类不存在
        ProductCategoryDO category = categoryRepository.selectById(parentId);
        if (category == null) {
            throw exception(CATEGORY_PARENT_NOT_EXISTS);
        }
        // 父分类不能是4级分类
        if (category.getLevel() == CATEGORY_LEVEL) {
            throw exception(CATEGORY_PARENT_NOT_LAST_LEVEL);
        }
    }

    @Override
    public List<ProductCategoryDO> getChildCategoryList(String parentId, Integer status) {

        List<ProductCategoryDO> children = new ArrayList<>();
        // 遍历每一层
        Collection<String> parentIds = Collections.singleton(parentId == null ? PARENT_ID_NULL : parentId); // 根分类的父分类ID为0
        for (int i = 0; i < Short.MAX_VALUE; i++) { // 使用 Short.MAX_VALUE 避免 bug 场景下，存在死循环
            // 查询当前层，所有的子分类
            List<ProductCategoryDO> cates = categoryRepository.selectListByParentId(parentIds);
            // 1. 如果没有子分类，则结束遍历
            if (CollUtil.isEmpty(cates)) {
                break;
            }
            // 2. 如果有子分类，继续遍历
            children.addAll(cates);
            parentIds = convert2Set(cates, ProductCategoryDO::getId, ObjectId::toHexString);
        }
        if (status != null) {
            children = children.stream().filter(cate -> cate.getStatus() == status).collect(Collectors.toList());
        }

        return children;
    }

}