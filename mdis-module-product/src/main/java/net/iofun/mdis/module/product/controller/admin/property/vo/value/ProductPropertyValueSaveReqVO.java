package net.iofun.mdis.module.product.controller.admin.property.vo.value;

import io.swagger.v3.oas.annotations.media.Schema;
import net.iofun.mdis.framework.common.vo.BaseVO;
import lombok.*;
import jakarta.validation.constraints.*;
import net.iofun.mdis.module.product.dal.dataobject.property.inner.ExpressionType;
import net.iofun.mdis.module.product.dal.dataobject.property.inner.NumberRangeType;

@Schema(description = "管理后台 - 产品属性值新增/修改 Request VO")
@Data
public class ProductPropertyValueSaveReqVO extends BaseVO {

    @Schema(description = "属性值命名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "属性值不能为空")
    private String valueName;

    @Schema(description = "计量单位ID")
    @NotBlank(message = "计量单位ID不能为空")
    private String unitId;

    @Schema(description = "属性值范围")
    private NumberRangeType valueRange;

    @Schema(description = "属性值公式")
    private ExpressionType valueExpr;

    @Schema(description = "属性项ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "属性项ID不能为空")
    private String propertyId;

    @Schema(description = "分类ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "分类ID不能为空")
    private String categoryId;

}