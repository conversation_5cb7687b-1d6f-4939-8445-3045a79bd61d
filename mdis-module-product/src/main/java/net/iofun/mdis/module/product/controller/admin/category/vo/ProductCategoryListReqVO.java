package net.iofun.mdis.module.product.controller.admin.category.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 产品分类列表 Request VO")
@Data
public class ProductCategoryListReqVO {

    @Schema(description = "分类名称")
    private String name;

    @Schema(description = "父类ID")
    private String parentId;

    @Schema(description = "展示状态，参见 CommonStatusEnum 枚举类")
    private Integer status;
}
