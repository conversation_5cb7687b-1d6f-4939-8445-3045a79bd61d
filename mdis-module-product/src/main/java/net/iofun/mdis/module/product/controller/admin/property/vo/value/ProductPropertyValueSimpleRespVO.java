package net.iofun.mdis.module.product.controller.admin.property.vo.value;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import net.iofun.mdis.framework.common.vo.BaseVO;

@Schema(description = "管理后台 - 产品属性值精简 Response VO")
@Data
public class ProductPropertyValueSimpleRespVO extends BaseVO {

    @Schema(description = "属性值", requiredMode = Schema.RequiredMode.REQUIRED)
    private String valueName;

    @Schema(description = "属性项ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String propertyId;

    @Schema(description = "分类ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String categoryId;

}
