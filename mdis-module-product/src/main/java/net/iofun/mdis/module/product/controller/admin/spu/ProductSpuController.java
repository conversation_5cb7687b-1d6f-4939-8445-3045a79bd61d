package net.iofun.mdis.module.product.controller.admin.spu;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import net.iofun.mdis.framework.common.pojo.PageParam;
import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.common.pojo.CommonResult;
import net.iofun.mdis.framework.common.util.object.BeanUtils;
import static net.iofun.mdis.framework.common.pojo.CommonResult.success;

import net.iofun.mdis.framework.excel.core.util.ExcelUtils;

import net.iofun.mdis.framework.apilog.core.annotation.ApiAccessLog;
import static net.iofun.mdis.framework.apilog.core.enums.OperateTypeEnum.*;

import net.iofun.mdis.module.product.controller.admin.spu.vo.*;
import net.iofun.mdis.module.product.dal.dataobject.spu.ProductSpuDO;
import net.iofun.mdis.module.product.service.spu.ProductSpuService;

@Tag(name = "管理后台 - 标准产品单元")
@RestController
@RequestMapping("/product/spu")
@Validated
public class ProductSpuController {

    @Resource
    private ProductSpuService spuService;

    @PostMapping("/create")
    @Operation(summary = "创建标准产品单元")
    @PreAuthorize("@ss.hasPermission('product:spu:create')")
    public CommonResult<String> createSpu(@Valid @RequestBody ProductSpuSaveReqVO createReqVO) {
        return success(spuService.createSpu(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新标准产品单元")
    @PreAuthorize("@ss.hasPermission('product:spu:update')")
    public CommonResult<Boolean> updateSpu(@Valid @RequestBody ProductSpuSaveReqVO updateReqVO) {
        spuService.updateSpu(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除标准产品单元")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('product:spu:delete')")
    public CommonResult<Boolean> deleteSpu(@RequestParam("id") String id) {
        spuService.deleteSpu(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得标准产品单元")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('product:spu:query')")
    public CommonResult<ProductSpuRespVO> getSpu(@RequestParam("id") String id) {
        ProductSpuDO spu = spuService.getSpu(id);
        return success(BeanUtils.toBean(spu, ProductSpuRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得标准产品单元分页")
    @PreAuthorize("@ss.hasPermission('product:spu:query')")
    public CommonResult<PageResult<ProductSpuRespVO>> getSpuPage(@Valid ProductSpuPageReqVO pageReqVO) {
        PageResult<ProductSpuDO> pageResult = spuService.getSpuPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ProductSpuRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出标准产品单元 Excel")
    @PreAuthorize("@ss.hasPermission('product:spu:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportSpuExcel(@Valid ProductSpuPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ProductSpuDO> list = spuService.getSpuPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "标准产品单元.xls", "数据", ProductSpuRespVO.class,
                        BeanUtils.toBean(list, ProductSpuRespVO.class));
    }

}