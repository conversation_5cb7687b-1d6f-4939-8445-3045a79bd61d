package net.iofun.mdis.module.product.controller.admin.unit.vo;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "管理后台 - 计量单位列表 Request VO")
@Data
public class ProductUnitListReqVO {

    @Schema(description = "符号")
    private String symbol;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "类型")
    private String type;

    @Schema(description = "开启状态")
    private Integer status;
}
