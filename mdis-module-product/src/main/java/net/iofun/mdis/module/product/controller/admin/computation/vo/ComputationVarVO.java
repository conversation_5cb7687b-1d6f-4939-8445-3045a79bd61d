package net.iofun.mdis.module.product.controller.admin.computation.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.*;

/**
 * 计算变量 VO
 *
 * <AUTHOR>
 */
@Schema(description = "计算变量")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ComputationVarVO {

    @Schema(description = "变量名称", required = true, example = "price")
    @NotEmpty(message = "变量名称不能为空")
    private String label;

    @Schema(description = "属性值ID", example = "64a5b2c3d4e5f6789012345")
    private String valueId;

    @Schema(description = "变量值（字符串形式，支持高精度）", example = "123.456789")
    private String value;

    @Schema(description = "SPU-ID", example = "64a5b2c3d4e5f6789012346")
    private String spuId;

    @Schema(description = "SKU-ID", example = "64a5b2c3d4e5f6789012347")
    private String skuId;

    @Schema(description = "分类ID", example = "64a5b2c3d4e5f6789012348")
    private String categoryId;
}