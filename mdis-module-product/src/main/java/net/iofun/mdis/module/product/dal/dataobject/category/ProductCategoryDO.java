package net.iofun.mdis.module.product.dal.dataobject.category;

import lombok.*;
import net.iofun.mdis.framework.common.entity.BaseEntity;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * 产品分类 DO
 *
 * <AUTHOR>
 */
@Document("product_category")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductCategoryDO extends BaseEntity {

    /**
     * 父分类编号 - 根分类
     */
    public static final String PARENT_ID_NULL = "0";
    /**
     * 限定分类层级
     */
    public static final int CATEGORY_LEVEL = 4;

    /**
     * 名称
     */
    private String name;
    /**
     * 父类ID
     */
    @Field("parent_id")
    private String parentId;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 开启状态
     *
     * 枚举 {@link TODO common_status 对应的类}
     */
    private Integer status;
    /**
     * 分类图地址
     */
    @Field("pic_url")
    private String picUrl;

    /**
     * 分类层级, 与前端 src/utils/constants.ts 中 ProductPropertyCateLevelEnum 对应
     * 1 - 门类
     * 2 - 系统
     * 3 - 产品
     * 4 - 配件
     */
    private Integer level;

}