package net.iofun.mdis.module.product.controller.admin.property.vo.property;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.iofun.mdis.framework.common.vo.BaseVO;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 产品属性项精简 Response VO")
@Data
public class ProductPropertySimpleRespVO extends BaseVO {

    @Schema(description = "属性名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @Schema(description = "属性值类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String valueType;
}
