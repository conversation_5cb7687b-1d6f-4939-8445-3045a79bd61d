package net.iofun.mdis.module.product.controller.admin.unit.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.iofun.mdis.framework.common.vo.BaseVO;

@Schema(description = "管理后台 - 计量单位  简单 Response VO")
@Data
public class ProductUnitSimpleRespVO extends BaseVO{
    @Schema(description = "符号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String symbol;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED)
    private String note;

}
