package net.iofun.mdis.module.product.controller.admin.spu.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import net.iofun.mdis.framework.common.vo.BaseVO;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import net.iofun.mdis.module.product.dal.dataobject.spu.inner.SpuPropertyInfo;
import net.iofun.mdis.module.product.dal.dataobject.spu.inner.SpuComponentInfo;

@Schema(description = "管理后台 - 标准产品单元新增/修改 Request VO")
@Data
public class ProductSpuSaveReqVO extends BaseVO {

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "名称不能为空")
    private String name;

    @Schema(description = "英文名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "英文名称不能为空")
    private String labelEn;

    @Schema(description = "分类ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "分类ID不能为空")
    private String categoryId;

    /**
     * 类型, "10": 配件单元, "20": 产品单元
     */
    @Schema(description = "类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "类型不能为空")
    private String type;

    @Schema(description = "单位ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "单位ID不能为空")
    private String unitId;

    @Schema(description = "基础属性", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "基础属性不能为空")
    private List<SpuPropertyInfo> properties;

    @Schema(description = "配件表")
    private List<SpuComponentInfo> components;

    @Schema(description = "图片组")
    private List<String> picUrls;

    @Schema(description = "开启状态", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "开启状态不能为空")
    private Integer status;

    @Schema(description = "编码")
    private String code;

}