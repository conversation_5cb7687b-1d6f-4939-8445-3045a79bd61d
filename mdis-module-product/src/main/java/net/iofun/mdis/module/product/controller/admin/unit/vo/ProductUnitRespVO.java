package net.iofun.mdis.module.product.controller.admin.unit.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import net.iofun.mdis.framework.common.vo.BaseVO;
import lombok.*;
import java.util.*;
import com.alibaba.excel.annotation.*;
import net.iofun.mdis.framework.excel.core.annotations.DictFormat;
import net.iofun.mdis.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 计量单位 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProductUnitRespVO extends BaseVO {

    @Schema(description = "符号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("符号")
    private String symbol;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("备注")
    private String note;

    @Schema(description = "类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("类型")
    private String type;

    @Schema(description = "序号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("序号")
    private Integer sort;

    @Schema(description = "开启状态", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "开启状态", converter = DictConvert.class)
    @DictFormat("common_status")
    private Integer status;

}