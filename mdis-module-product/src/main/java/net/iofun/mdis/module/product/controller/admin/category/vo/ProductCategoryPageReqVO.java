package net.iofun.mdis.module.product.controller.admin.category.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import net.iofun.mdis.framework.common.pojo.PageParam;

@Schema(description = "管理后台 - 产品分类分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductCategoryPageReqVO extends PageParam {

    @Schema(description = "名称")
    private String name;

    @Schema(description = "父类ID")
    private String parentId;

    @Schema(description = "开启状态")
    private Integer status;

}