package net.iofun.mdis.module.product.controller.admin.property.vo.value;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import net.iofun.mdis.framework.common.vo.BaseVO;

@Schema(description = "管理后台 - 产品属性值列表 Request VO")
@Data
public class ProductPropertyValueListReqVO extends BaseVO {

    @Schema(description = "属性值")
    private String valueName;

    @Schema(description = "属性项ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "属性项ID不能为空") // 在前端创建SPU时,通过确定属性项再选择属性值,所以约束,被ProductPropertyValueSelect.vue组件调用
    private String propertyId;

    @Schema(description = "分类ID")
    private String categoryId;

}
