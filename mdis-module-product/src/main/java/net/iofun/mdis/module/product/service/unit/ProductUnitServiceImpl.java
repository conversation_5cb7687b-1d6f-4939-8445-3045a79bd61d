package net.iofun.mdis.module.product.service.unit;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import static net.iofun.mdis.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;
import org.bson.types.ObjectId;
import net.iofun.mdis.module.product.controller.admin.unit.vo.*;
import net.iofun.mdis.module.product.dal.dataobject.unit.ProductUnitDO;
import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.common.pojo.PageParam;
import net.iofun.mdis.framework.common.util.object.BeanUtils;

import net.iofun.mdis.module.product.dal.repository.unit.ProductUnitRepository;

import static net.iofun.mdis.framework.common.exception.util.ServiceExceptionUtil.exception;
import static net.iofun.mdis.module.product.enums.ErrorCodeConstants.*;

/**
 * 计量单位 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProductUnitServiceImpl implements ProductUnitService {

    @Resource
    private ProductUnitRepository unitRepository;

    @Override
    public String createUnit(ProductUnitSaveReqVO createReqVO) {
        // 插入
        ProductUnitDO unit = BeanUtils.toBean(createReqVO, ProductUnitDO.class);
        unitRepository.insert(unit, getLoginUser());
        // 返回
        return unit.getId().toHexString();
    }

    @Override
    public void updateUnit(ProductUnitSaveReqVO updateReqVO) {
        // 校验存在
        validateUnitExists(updateReqVO.getId());
        // 更新
        ProductUnitDO updateObj = BeanUtils.toBean(updateReqVO, ProductUnitDO.class);
        unitRepository.updateById(updateObj, getLoginUser());
    }

    @Override
    public void deleteUnit(String id) {
        ObjectId oid = new ObjectId(id);
        // 校验存在
        validateUnitExists(oid);
        // 删除
        unitRepository.deleteById(oid, getLoginUser());
    }

    private void validateUnitExists(ObjectId id) {
        if (!unitRepository.validateExists(id)) {
            throw exception(UNIT_NOT_EXISTS);
        }
    }

    @Override
    public ProductUnitDO getUnit(String id) {
        return unitRepository.selectById(id);
    }

    @Override
    public PageResult<ProductUnitDO> getUnitPage(ProductUnitPageReqVO pageReqVO) {
        return unitRepository.selectPage(pageReqVO);
    }

    @Override
    public List<ProductUnitDO> getUnitList(ProductUnitListReqVO listReqVO) {
        return unitRepository.selectList(listReqVO);
    }

}