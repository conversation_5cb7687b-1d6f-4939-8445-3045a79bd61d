package net.iofun.mdis.module.product.controller.admin.computation.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 计算响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "计算响应")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ComputationRespVO {

    @Schema(description = "计算结果值（字符串形式，保持高精度）", example = "123.456789")
    private String result;

    @Schema(description = "是否计算成功", example = "true")
    private Boolean success;

    @Schema(description = "错误信息", example = "变量不存在")
    private String errorMessage;

    /**
     * 创建成功响应
     */
    public static ComputationRespVO success(String result) {
        return ComputationRespVO.builder()
                .result(result)
                .success(true)
                .build();
    }

    /**
     * 创建失败响应
     */
    public static ComputationRespVO failure(String errorMessage) {
        return ComputationRespVO.builder()
                .success(false)
                .errorMessage(errorMessage)
                .build();
    }
}