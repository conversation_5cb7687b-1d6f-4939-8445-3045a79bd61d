package net.iofun.mdis.module.product.dal.repository.spu;

import java.util.*;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Repository;
import net.iofun.mdis.framework.mongo.core.repository.BaseRepository;
import net.iofun.mdis.framework.security.core.LoginUser;
import com.mongodb.client.result.UpdateResult;

import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.mongo.core.query.LambdaQueryWrapperMongo;
import net.iofun.mdis.module.product.dal.dataobject.spu.ProductSpuDO;
import net.iofun.mdis.module.product.controller.admin.spu.vo.*;

/**
 * 标准产品单元 Repository
 *
 * <AUTHOR>
 */
@Repository
public class ProductSpuRepository extends BaseRepository<ProductSpuDO, ObjectId> {
    public ProductSpuRepository(MongoTemplate mongoOperations) {
        super(ProductSpuDO.class, mongoOperations);
    }

    public PageResult<ProductSpuDO> selectPage(ProductSpuPageReqVO reqVO) {
        LambdaQueryWrapperMongo<ProductSpuDO> queryWrapper = new LambdaQueryWrapperMongo<>(ProductSpuDO.class)
                .likeIfPresent(ProductSpuDO::getName, reqVO.getName())
                .likeIfPresent(ProductSpuDO::getLabelEn, reqVO.getLabelEn())
                .eqIfPresent(ProductSpuDO::getCategoryId, reqVO.getCategoryId())
                .eqIfPresent(ProductSpuDO::getType, reqVO.getType())
                .likeIfPresent(ProductSpuDO::getCode, reqVO.getCode())
                .orderByDesc(ProductSpuDO::getId);
        return selectPage(reqVO, queryWrapper);
    }


    public ProductSpuDO selectById(String id) {
        LambdaQueryWrapperMongo<ProductSpuDO> queryWrapper = new LambdaQueryWrapperMongo<>(ProductSpuDO.class)
                .eq(ProductSpuDO::getId, id);
        return findOne(queryWrapper).orElse(null);
    }

    public boolean validateExists(ObjectId id) {
        LambdaQueryWrapperMongo<ProductSpuDO> queryWrapper = new LambdaQueryWrapperMongo<>(ProductSpuDO.class)
                .eq(ProductSpuDO::getId, id);
        return existsByQuery(queryWrapper);
    }

    public UpdateResult deleteById(ObjectId id, LoginUser loginUser) {
        LambdaQueryWrapperMongo<ProductSpuDO> queryWrapper = new LambdaQueryWrapperMongo<>(ProductSpuDO.class)
                .eq(ProductSpuDO::getId, id);
        return softDeleteByQuery(queryWrapper, loginUser);
    }

}