package net.iofun.mdis.module.product.controller.admin.unit.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import net.iofun.mdis.framework.common.vo.BaseVO;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 计量单位新增/修改 Request VO")
@Data
public class ProductUnitSaveReqVO extends BaseVO {

    @Schema(description = "符号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "符号不能为空")
    private String symbol;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "备注不能为空")
    private String note;

    @Schema(description = "类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "类型不能为空")
    private String type;

    @Schema(description = "序号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "序号不能为空")
    private Integer sort;

    @Schema(description = "开启状态", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "开启状态不能为空")
    private Integer status;

}