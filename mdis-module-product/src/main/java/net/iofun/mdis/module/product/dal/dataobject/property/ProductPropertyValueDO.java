package net.iofun.mdis.module.product.dal.dataobject.property;

import lombok.*;
import net.iofun.mdis.framework.common.entity.BaseEntity;
import net.iofun.mdis.module.product.dal.dataobject.property.inner.ExpressionType;
import net.iofun.mdis.module.product.dal.dataobject.property.inner.NumberRangeType;

import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * 产品属性值 DO
 *
 * <AUTHOR>
 */
@Document("product_property_value")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@CompoundIndex(name = "propertyId_categoryId_valueName_idx", def = "{'property_id': 1, 'category_id': 1, 'value_name': 1}", unique = true) // 唯一复合索引
public class ProductPropertyValueDO extends BaseEntity {

    /**
     * 属性值命名
     */
    @Field("value_name")
    private String valueName;
    /**
     * 计量单位ID
     */
    @Field("unit_id")
    private String unitId;
    /**
     * 属性值范围
     */
    @Field("value_range")
    private NumberRangeType valueRange;
    /**
     * 属性值公式
     */
    @Field("value_expr")
    private ExpressionType valueExpr;
    /**
     * 属性项ID
     */
    @Field("property_id")
    private String propertyId;
    /**
     * 分类ID
     */
    @Field("category_id")
    private String categoryId;

}