package net.iofun.mdis.module.product.dal.dataobject.unit;

import lombok.*;
import java.util.*;
import net.iofun.mdis.framework.common.entity.BaseEntity;
import org.springframework.data.mongodb.core.mapping.Document;
import net.iofun.mdis.framework.common.enums.CommonStatusEnum;

/**
 * 计量单位 DO
 *
 * <AUTHOR>
 */
@Document("product_unit")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductUnitDO extends BaseEntity {

    /**
     * 无单位
     */
    public static final String UNIT_NULL = "0";

    /**
     * 符号
     */
    private String symbol;
    /**
     * 备注
     */
    private String note;
    /**
     * 类型
     * 枚举 {@link TODO product_unit_type 对应的类}
     */
    private String type;
    /**
     * 序号
     */
    private Integer sort;
    /**
     * 开启状态
     *
     * 枚举 {@link COMMON_STATUS 对应的类}
     */
    private Integer status;

}