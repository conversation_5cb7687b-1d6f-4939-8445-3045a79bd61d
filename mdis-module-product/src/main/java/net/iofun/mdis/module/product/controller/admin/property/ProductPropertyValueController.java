package net.iofun.mdis.module.product.controller.admin.property;

import net.iofun.mdis.module.product.controller.admin.property.vo.value.ProductPropertyValueListReqVO;
import net.iofun.mdis.module.product.controller.admin.property.vo.value.ProductPropertyValuePageReqVO;
import net.iofun.mdis.module.product.controller.admin.property.vo.value.ProductPropertyValueRespVO;
import net.iofun.mdis.module.product.controller.admin.property.vo.value.ProductPropertyValueSaveReqVO;
import net.iofun.mdis.module.product.controller.admin.property.vo.value.ProductPropertyValueSimpleRespVO;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import net.iofun.mdis.framework.common.pojo.PageParam;
import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.common.pojo.CommonResult;
import net.iofun.mdis.framework.common.util.object.BeanUtils;
import static net.iofun.mdis.framework.common.pojo.CommonResult.success;

import net.iofun.mdis.framework.excel.core.util.ExcelUtils;

import net.iofun.mdis.framework.apilog.core.annotation.ApiAccessLog;
import static net.iofun.mdis.framework.apilog.core.enums.OperateTypeEnum.*;

import net.iofun.mdis.module.product.dal.dataobject.property.ProductPropertyValueDO;
import net.iofun.mdis.module.product.service.property.ProductPropertyValueService;

@Tag(name = "管理后台 - 产品属性值")
@RestController
@RequestMapping("/product/property/value")
@Validated
public class ProductPropertyValueController {

    @Resource
    private ProductPropertyValueService propertyValueService;

    @PostMapping("/create")
    @Operation(summary = "创建产品属性值")
    @PreAuthorize("@ss.hasPermission('product:property-value:create')")
    public CommonResult<String> createPropertyValue(@Valid @RequestBody ProductPropertyValueSaveReqVO createReqVO) {
        return success(propertyValueService.createPropertyValue(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新产品属性值")
    @PreAuthorize("@ss.hasPermission('product:property-value:update')")
    public CommonResult<Boolean> updatePropertyValue(@Valid @RequestBody ProductPropertyValueSaveReqVO updateReqVO) {
        propertyValueService.updatePropertyValue(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除产品属性值")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('product:property:delete')")
    public CommonResult<Boolean> deletePropertyValue(@RequestParam("id") String id) {
        propertyValueService.deletePropertyValue(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得产品属性值")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('product:property:query')")
    public CommonResult<ProductPropertyValueRespVO> getPropertyValue(@RequestParam("id") String id) {
        ProductPropertyValueDO propertyValue = propertyValueService.getPropertyValue(id);
        return success(BeanUtils.toBean(propertyValue, ProductPropertyValueRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得产品属性值分页")
    @PreAuthorize("@ss.hasPermission('product:property:query')")
    public CommonResult<PageResult<ProductPropertyValueRespVO>> getPropertyValuePage(@Valid ProductPropertyValuePageReqVO pageReqVO) {
        PageResult<ProductPropertyValueDO> pageResult = propertyValueService.getPropertyValuePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ProductPropertyValueRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出产品属性值 Excel")
    @PreAuthorize("@ss.hasPermission('product:property:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportPropertyValueExcel(@Valid ProductPropertyValuePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ProductPropertyValueDO> list = propertyValueService.getPropertyValuePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "产品属性值.xls", "数据", ProductPropertyValueRespVO.class,
                        BeanUtils.toBean(list, ProductPropertyValueRespVO.class));
    }

    @GetMapping("/simple-list")
    @Operation(summary = "获得产品属性值分页")
    @PreAuthorize("@ss.hasPermission('product:property:query')")
    public CommonResult<List<ProductPropertyValueSimpleRespVO>> getPropertyValueSimpleList(@Valid ProductPropertyValueListReqVO listReqVO) {
        List<ProductPropertyValueSimpleRespVO> allList = propertyValueService.getPropertyValueSimpleList(listReqVO);
        return success(allList);
    }
}