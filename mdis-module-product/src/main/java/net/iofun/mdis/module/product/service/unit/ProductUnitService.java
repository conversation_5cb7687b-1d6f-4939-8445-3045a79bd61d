package net.iofun.mdis.module.product.service.unit;

import java.util.*;
import jakarta.validation.*;
import net.iofun.mdis.module.product.controller.admin.unit.vo.*;
import net.iofun.mdis.module.product.dal.dataobject.unit.ProductUnitDO;
import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.common.pojo.PageParam;

/**
 * 计量单位 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductUnitService {

    /**
     * 创建计量单位
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createUnit(@Valid ProductUnitSaveReqVO createReqVO);

    /**
     * 更新计量单位
     *
     * @param updateReqVO 更新信息
     */
    void updateUnit(@Valid ProductUnitSaveReqVO updateReqVO);

    /**
     * 删除计量单位
     *
     * @param id 编号
     */
    void deleteUnit(String id);

    /**
     * 获得计量单位
     *
     * @param id 编号
     * @return 计量单位
     */
    ProductUnitDO getUnit(String id);

    /**
     * 获得计量单位分页
     *
     * @param pageReqVO 分页查询
     * @return 计量单位分页
     */
    PageResult<ProductUnitDO> getUnitPage(ProductUnitPageReqVO pageReqVO);

    /**
     * 获得计量单位列表
     *
     * @param listReqVO 查询条件
     * @return 计量单位列表
     */
    List<ProductUnitDO> getUnitList(ProductUnitListReqVO listReqVO);
}