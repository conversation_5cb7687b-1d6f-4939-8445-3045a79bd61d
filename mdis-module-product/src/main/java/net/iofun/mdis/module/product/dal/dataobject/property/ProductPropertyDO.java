package net.iofun.mdis.module.product.dal.dataobject.property;

import lombok.*;
import java.util.*;
import net.iofun.mdis.framework.common.entity.BaseEntity;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * 产品属性项 DO
 *
 * <AUTHOR>
 */
@Document("product_property")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductPropertyDO extends BaseEntity {

    /**
     * 属性名称
     */
    private String name;
    /**
     * 英文名称
     */
    @Field("label_en")
    private String labelEn;
    /**
     * 分类级别, 3-三级(产品属性), 4-四级(配件属性)
     */
    @Field("cate_level")
    private Integer cateLevel;
    /**
     * 属性类型, '10'-基础属性, '20'-工艺属性, '30'-增值属性
     */
    @Field("attr_type")
    private String attrType;
    /**
     * 属性值类型, '10'-文字枚举, '20'-数值范围, '30'-计算公式
     */
    @Field("value_type")
    private String valueType;
    /**
     * 是否显示
     */
    @Field("is_display")
    private Boolean isDisplay;
    /**
     * 排序
     */
    private Integer sort;

}