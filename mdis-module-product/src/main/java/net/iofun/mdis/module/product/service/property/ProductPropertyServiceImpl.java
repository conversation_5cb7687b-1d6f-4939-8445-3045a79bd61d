package net.iofun.mdis.module.product.service.property;

import net.iofun.mdis.module.product.controller.admin.property.vo.property.ProductPropertyPageReqVO;
import net.iofun.mdis.module.product.controller.admin.property.vo.property.ProductPropertySaveReqVO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import static net.iofun.mdis.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;
import org.bson.types.ObjectId;
import net.iofun.mdis.module.product.dal.dataobject.property.ProductPropertyDO;
import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.common.util.object.BeanUtils;

import net.iofun.mdis.module.product.dal.repository.property.ProductPropertyRepository;

import java.util.List;
import java.util.Set;

import static net.iofun.mdis.framework.common.exception.util.ServiceExceptionUtil.exception;
import static net.iofun.mdis.module.product.enums.ErrorCodeConstants.*;

/**
 * 产品属性项 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProductPropertyServiceImpl implements ProductPropertyService {

    public static final Set<Integer> VALID_CATE_LEVELS = Set.of(3, 4); // 产品属性项分类级别必须是3或4级

    @Resource
    private ProductPropertyRepository propertyRepository;

    @Override
    public String createProperty(ProductPropertySaveReqVO createReqVO) {
        // 校验是否有中或英文同名属性项
        if (!propertyRepository.selectByNameOrLabelEn(createReqVO).isEmpty()) {
            throw exception(PROPERTY_NAME_LABEL_EN_DUPLICATED);
        }
        //检验分类级别是否在数值范围内
        if (!VALID_CATE_LEVELS.contains(createReqVO.getCateLevel())) {
            throw exception(PROPERTY_CATE_LEVEL_INVALID);
        }
        // 插入
        ProductPropertyDO property = BeanUtils.toBean(createReqVO, ProductPropertyDO.class);
        propertyRepository.insert(property, getLoginUser());
        // 返回
        return property.getId().toHexString();
    }

    @Override
    public void updateProperty(ProductPropertySaveReqVO updateReqVO) {
        // 校验是否有中或英文同名属性项
        if (propertyRepository.selectByNameOrLabelEn(updateReqVO).size() > 1) {
            throw exception(PROPERTY_NAME_LABEL_EN_DUPLICATED);
        }
        //检验分类级别是否在数值范围内
        if (!VALID_CATE_LEVELS.contains(updateReqVO.getCateLevel())) {
            throw exception(PROPERTY_CATE_LEVEL_INVALID);
        }
        // 校验存在
        validatePropertyExists(updateReqVO.getId());
        // 更新
        ProductPropertyDO updateObj = BeanUtils.toBean(updateReqVO, ProductPropertyDO.class);
        propertyRepository.updateById(updateObj, getLoginUser());
    }

    @Override
    public void deleteProperty(String id) {
        ObjectId oid = new ObjectId(id);
        // 校验存在
        validatePropertyExists(oid);
        // 删除
        propertyRepository.deleteById(oid, getLoginUser());
    }

    private void validatePropertyExists(ObjectId id) {
        if (!propertyRepository.validateExists(id)) {
            throw exception(PROPERTY_NOT_EXISTS);
        }
    }

    @Override
    public ProductPropertyDO getProperty(String id) {
        return propertyRepository.selectById(id);
    }

    @Override
    public PageResult<ProductPropertyDO> getPropertyPage(ProductPropertyPageReqVO pageReqVO) {
        return propertyRepository.selectPage(pageReqVO);
    }

    @Override
    public List<ProductPropertyDO> getPropertyAllList() {
        return propertyRepository.selectList();
    }

}