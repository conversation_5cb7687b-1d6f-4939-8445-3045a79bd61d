package net.iofun.mdis.module.product.dal.repository.property;

import net.iofun.mdis.module.product.controller.admin.property.vo.value.ProductPropertyValueListReqVO;
import net.iofun.mdis.module.product.controller.admin.property.vo.value.ProductPropertyValuePageReqVO;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Repository;
import net.iofun.mdis.framework.mongo.core.repository.BaseRepository;
import net.iofun.mdis.framework.security.core.LoginUser;
import com.mongodb.client.result.UpdateResult;

import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.mongo.core.query.LambdaQueryWrapperMongo;
import net.iofun.mdis.module.product.dal.dataobject.property.ProductPropertyValueDO;

/**
 * 产品属性值 Repository
 *
 * <AUTHOR>
 */
@Repository
public class ProductPropertyValueRepository extends BaseRepository<ProductPropertyValueDO, ObjectId> {
    public ProductPropertyValueRepository(MongoTemplate mongoOperations) {
        super(ProductPropertyValueDO.class, mongoOperations);
    }

    public PageResult<ProductPropertyValueDO> selectPage(ProductPropertyValuePageReqVO reqVO) {
        LambdaQueryWrapperMongo<ProductPropertyValueDO> queryWrapper = new LambdaQueryWrapperMongo<>(ProductPropertyValueDO.class)
                .likeIfPresent(ProductPropertyValueDO::getValueName, reqVO.getValueName())
                .eqIfPresent(ProductPropertyValueDO::getPropertyId, reqVO.getPropertyId())
                .eqIfPresent(ProductPropertyValueDO::getCategoryId, reqVO.getCategoryId())
                .orderByDesc(ProductPropertyValueDO::getId);
        return selectPage(reqVO, queryWrapper);
    }

    public List<ProductPropertyValueDO> selectList(ProductPropertyValueListReqVO reqVO) {
        // 如果分类ID不为空，则查询分类ID和0的属性值(0表示通用属性值), 否则inIfPresent不生效(返回全部)
        Set<String> categoryIds = new HashSet<>();
        if(reqVO.getCategoryId() != null){
            categoryIds.add(reqVO.getCategoryId());
            categoryIds.add("0");
        }

        LambdaQueryWrapperMongo<ProductPropertyValueDO> queryWrapper = new LambdaQueryWrapperMongo<>(ProductPropertyValueDO.class)
                .likeIfPresent(ProductPropertyValueDO::getValueName, reqVO.getValueName())
                .eqIfPresent(ProductPropertyValueDO::getPropertyId, reqVO.getPropertyId())
                .inIfPresent(ProductPropertyValueDO::getCategoryId, categoryIds)
                .orderByDesc(ProductPropertyValueDO::getId);
        return selectList(queryWrapper);
    }
    
    public ProductPropertyValueDO selectById(String id) {
        LambdaQueryWrapperMongo<ProductPropertyValueDO> queryWrapper = new LambdaQueryWrapperMongo<>(ProductPropertyValueDO.class)
                .eq(ProductPropertyValueDO::getId, id);
        return findOne(queryWrapper).orElse(null);
    }

    public boolean validateExists(ObjectId id) {
        LambdaQueryWrapperMongo<ProductPropertyValueDO> queryWrapper = new LambdaQueryWrapperMongo<>(ProductPropertyValueDO.class)
                .eq(ProductPropertyValueDO::getId, id);
        return existsByQuery(queryWrapper);
    }

    public UpdateResult deleteById(ObjectId id, LoginUser loginUser) {
        LambdaQueryWrapperMongo<ProductPropertyValueDO> queryWrapper = new LambdaQueryWrapperMongo<>(ProductPropertyValueDO.class)
                .eq(ProductPropertyValueDO::getId, id);
        return softDeleteByQuery(queryWrapper, loginUser);
    }

}