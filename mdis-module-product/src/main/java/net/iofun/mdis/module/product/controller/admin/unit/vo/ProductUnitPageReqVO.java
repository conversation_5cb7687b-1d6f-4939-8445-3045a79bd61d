package net.iofun.mdis.module.product.controller.admin.unit.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import net.iofun.mdis.framework.common.pojo.PageParam;

@Schema(description = "管理后台 - 计量单位分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductUnitPageReqVO extends PageParam {

    @Schema(description = "符号")
    private String symbol;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "类型")
    private String type;

    @Schema(description = "开启状态")
    private Integer status;

}