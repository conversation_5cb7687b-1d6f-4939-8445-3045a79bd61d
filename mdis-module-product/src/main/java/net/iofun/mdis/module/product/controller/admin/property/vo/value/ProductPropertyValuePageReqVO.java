package net.iofun.mdis.module.product.controller.admin.property.vo.value;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import net.iofun.mdis.framework.common.pojo.PageParam;

@Schema(description = "管理后台 - 产品属性值分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductPropertyValuePageReqVO extends PageParam {

    @Schema(description = "属性值")
    private String valueName;

    @Schema(description = "属性项ID")
    private String propertyId;

    @Schema(description = "分类ID")
    private String categoryId;

}