package net.iofun.mdis.module.product.dal.repository.property;

import net.iofun.mdis.module.product.controller.admin.property.vo.property.ProductPropertyPageReqVO;
import net.iofun.mdis.module.product.controller.admin.property.vo.property.ProductPropertySaveReqVO;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Repository;
import net.iofun.mdis.framework.mongo.core.repository.BaseRepository;
import net.iofun.mdis.framework.security.core.LoginUser;
import com.mongodb.client.result.UpdateResult;

import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.mongo.core.query.LambdaQueryWrapperMongo;
import net.iofun.mdis.module.product.dal.dataobject.property.ProductPropertyDO;

import java.util.ArrayList;
import java.util.List;

/**
 * 产品属性项 Repository
 *
 * <AUTHOR>
 */
@Repository
public class ProductPropertyRepository extends BaseRepository<ProductPropertyDO, ObjectId> {
    public ProductPropertyRepository(MongoTemplate mongoOperations) {
        super(ProductPropertyDO.class, mongoOperations);
    }

    public PageResult<ProductPropertyDO> selectPage(ProductPropertyPageReqVO reqVO) {
        LambdaQueryWrapperMongo<ProductPropertyDO> queryWrapper = new LambdaQueryWrapperMongo<>(ProductPropertyDO.class)
                .likeIfPresent(ProductPropertyDO::getName, reqVO.getName())
                .likeIfPresent(ProductPropertyDO::getLabelEn, reqVO.getLabelEn())
                .eqIfPresent(ProductPropertyDO::getCateLevel, reqVO.getCateLevel())
                .eqIfPresent(ProductPropertyDO::getAttrType, reqVO.getAttrType())
                .eqIfPresent(ProductPropertyDO::getValueType, reqVO.getValueType())
                .eqIfPresent(ProductPropertyDO::getIsDisplay, reqVO.getIsDisplay())
                .orderByDesc(ProductPropertyDO::getId);
        return selectPage(reqVO, queryWrapper);
    }

    public ProductPropertyDO selectById(String id) {
        LambdaQueryWrapperMongo<ProductPropertyDO> queryWrapper = new LambdaQueryWrapperMongo<>(ProductPropertyDO.class)
                .eq(ProductPropertyDO::getId, id);
        return findOne(queryWrapper).orElse(null);
    }

    public boolean validateExists(ObjectId id) {
        LambdaQueryWrapperMongo<ProductPropertyDO> queryWrapper = new LambdaQueryWrapperMongo<>(ProductPropertyDO.class)
                .eq(ProductPropertyDO::getId, id);
        return existsByQuery(queryWrapper);
    }

    public UpdateResult deleteById(ObjectId id, LoginUser loginUser) {
        LambdaQueryWrapperMongo<ProductPropertyDO> queryWrapper = new LambdaQueryWrapperMongo<>(ProductPropertyDO.class)
                .eq(ProductPropertyDO::getId, id);
        return softDeleteByQuery(queryWrapper, loginUser);
    }

    public List<ProductPropertyDO> selectByNameOrLabelEn(ProductPropertySaveReqVO reqVO) {
        List<Criteria> criteriaList = new ArrayList<>();
        LambdaQueryWrapperMongo<ProductPropertyDO> queryWrapper = new LambdaQueryWrapperMongo<>(ProductPropertyDO.class);
        criteriaList.add(queryWrapper.eqIfPresentCriteria(ProductPropertyDO::getName, reqVO.getName()));
        criteriaList.add(queryWrapper.eqIfPresentCriteria(ProductPropertyDO::getLabelEn, reqVO.getLabelEn()));
        queryWrapper.or(criteriaList);
        return findAll(queryWrapper);
    }

    public List<ProductPropertyDO> selectList() {
        LambdaQueryWrapperMongo<ProductPropertyDO> queryWrapper = new LambdaQueryWrapperMongo<>(ProductPropertyDO.class);
        return selectList(queryWrapper);
    }

}