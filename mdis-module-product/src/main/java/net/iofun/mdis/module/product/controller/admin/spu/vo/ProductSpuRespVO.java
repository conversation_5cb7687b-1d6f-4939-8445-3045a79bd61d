package net.iofun.mdis.module.product.controller.admin.spu.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import net.iofun.mdis.framework.common.vo.BaseVO;
import lombok.*;
import java.util.*;
import com.alibaba.excel.annotation.*;
import net.iofun.mdis.framework.excel.core.annotations.DictFormat;
import net.iofun.mdis.framework.excel.core.convert.DictConvert;
import net.iofun.mdis.module.product.dal.dataobject.spu.inner.SpuPropertyInfo;
import net.iofun.mdis.module.product.dal.dataobject.spu.inner.SpuComponentInfo;

@Schema(description = "管理后台 - 标准产品单元 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProductSpuRespVO extends BaseVO {

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("名称")
    private String name;

    @Schema(description = "英文名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("英文名称")
    private String labelEn;

    @Schema(description = "分类ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("分类ID")
    private String categoryId;

    @Schema(description = "类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("类型")
    private String type;

    @Schema(description = "单位ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("单位ID")
    private String unitId;

    @Schema(description = "基础属性", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("基础属性")
    private List<SpuPropertyInfo> properties;

    @Schema(description = "配件表")
    @ExcelProperty("配件表")
    private List<SpuComponentInfo> components;

    @Schema(description = "图片组")
    @ExcelProperty("图片组")
    private List<String> picUrls;

    @Schema(description = "开启状态", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "开启状态", converter = DictConvert.class)
    @DictFormat("common_status")
    private Integer status;

    @Schema(description = "编码")
    @ExcelProperty("编码")
    private String code;

}