package net.iofun.mdis.module.product.controller.admin.unit;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import net.iofun.mdis.framework.common.pojo.PageParam;
import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.common.pojo.CommonResult;
import net.iofun.mdis.framework.common.util.object.BeanUtils;
import static net.iofun.mdis.framework.common.pojo.CommonResult.success;

import net.iofun.mdis.framework.excel.core.util.ExcelUtils;

import net.iofun.mdis.framework.apilog.core.annotation.ApiAccessLog;
import static net.iofun.mdis.framework.apilog.core.enums.OperateTypeEnum.*;

import net.iofun.mdis.module.product.controller.admin.unit.vo.*;
import net.iofun.mdis.module.product.dal.dataobject.unit.ProductUnitDO;
import net.iofun.mdis.module.product.service.unit.ProductUnitService;

@Tag(name = "管理后台 - 计量单位")
@RestController
@RequestMapping("/product/unit")
@Validated
public class ProductUnitController {

    @Resource
    private ProductUnitService unitService;

    @PostMapping("/create")
    @Operation(summary = "创建计量单位")
    @PreAuthorize("@ss.hasPermission('product:unit:create')")
    public CommonResult<String> createUnit(@Valid @RequestBody ProductUnitSaveReqVO createReqVO) {
        return success(unitService.createUnit(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新计量单位")
    @PreAuthorize("@ss.hasPermission('product:unit:update')")
    public CommonResult<Boolean> updateUnit(@Valid @RequestBody ProductUnitSaveReqVO updateReqVO) {
        unitService.updateUnit(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除计量单位")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('product:unit:delete')")
    public CommonResult<Boolean> deleteUnit(@RequestParam("id") String id) {
        unitService.deleteUnit(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得计量单位")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('product:unit:query')")
    public CommonResult<ProductUnitRespVO> getUnit(@RequestParam("id") String id) {
        ProductUnitDO unit = unitService.getUnit(id);
        return success(BeanUtils.toBean(unit, ProductUnitRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得计量单位分页")
    @PreAuthorize("@ss.hasPermission('product:unit:query')")
    public CommonResult<PageResult<ProductUnitRespVO>> getUnitPage(@Valid ProductUnitPageReqVO pageReqVO) {
        PageResult<ProductUnitDO> pageResult = unitService.getUnitPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ProductUnitRespVO.class));
    }
    @GetMapping("/simple-list")
    @Operation(summary = "获得计量单位列表")
    @PreAuthorize("@ss.hasPermission('product:unit:query')")
    public CommonResult<List<ProductUnitSimpleRespVO>> getUnitList(@Valid ProductUnitListReqVO listReqVO) {
        List<ProductUnitDO> list = unitService.getUnitList(listReqVO);
        return success(BeanUtils.toBean(list, ProductUnitSimpleRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出计量单位 Excel")
    @PreAuthorize("@ss.hasPermission('product:unit:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportUnitExcel(@Valid ProductUnitPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ProductUnitDO> list = unitService.getUnitPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "计量单位.xls", "数据", ProductUnitRespVO.class,
                        BeanUtils.toBean(list, ProductUnitRespVO.class));
    }

}