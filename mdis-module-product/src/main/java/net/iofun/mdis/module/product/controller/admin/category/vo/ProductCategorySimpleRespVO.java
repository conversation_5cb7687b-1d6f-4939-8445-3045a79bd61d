package net.iofun.mdis.module.product.controller.admin.category.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.iofun.mdis.framework.common.vo.BaseVO;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 产品分类简单 Response VO")
@Data
public class ProductCategorySimpleRespVO extends BaseVO {

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @Schema(description = "父类ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String parentId;

    @Schema(description = "分类层级", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer level;
}
