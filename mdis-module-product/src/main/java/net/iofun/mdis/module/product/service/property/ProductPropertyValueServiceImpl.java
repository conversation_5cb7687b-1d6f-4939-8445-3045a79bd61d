package net.iofun.mdis.module.product.service.property;

import net.iofun.mdis.module.product.controller.admin.property.vo.value.ProductPropertyValueListReqVO;
import net.iofun.mdis.module.product.controller.admin.property.vo.value.ProductPropertyValuePageReqVO;
import net.iofun.mdis.module.product.controller.admin.property.vo.value.ProductPropertyValueSaveReqVO;
import net.iofun.mdis.module.product.controller.admin.property.vo.value.ProductPropertyValueSimpleRespVO;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import static net.iofun.mdis.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;
import org.bson.types.ObjectId;
import org.springframework.util.Assert;

import net.iofun.mdis.module.product.dal.dataobject.property.ProductPropertyDO;
import net.iofun.mdis.module.product.dal.dataobject.property.ProductPropertyValueDO;
import net.iofun.mdis.module.product.dal.dataobject.unit.ProductUnitDO;
import net.iofun.mdis.framework.common.enums.ProductPropertyValueTypeEnum;
import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.common.util.object.BeanUtils;

import net.iofun.mdis.module.product.dal.repository.property.ProductPropertyValueRepository;
import net.iofun.mdis.module.product.service.unit.ProductUnitService;

import static net.iofun.mdis.framework.common.exception.util.ServiceExceptionUtil.exception;
import static net.iofun.mdis.module.product.enums.ErrorCodeConstants.*;

import java.util.List;

/**
 * 产品属性值 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProductPropertyValueServiceImpl implements ProductPropertyValueService {

    @Resource
    private ProductPropertyService propertyService;

    @Resource
    private ProductPropertyValueRepository propertyValueRepository;

    @Resource
    private ProductUnitService unitService;

    @Override
    public String createPropertyValue(ProductPropertyValueSaveReqVO createReqVO) {
        // 校验详情
        validatePropertyValueDetail(createReqVO);

        // 插入
        ProductPropertyValueDO propertyValue = BeanUtils.toBean(createReqVO, ProductPropertyValueDO.class);
        propertyValueRepository.insert(propertyValue, getLoginUser());
        // 返回
        return propertyValue.getId().toHexString();
    }

    @Override
    public void updatePropertyValue(ProductPropertyValueSaveReqVO updateReqVO) {
        // 校验存在
        validatePropertyValueExists(updateReqVO.getId());
        // 校验详情
        validatePropertyValueDetail(updateReqVO);
        // 更新
        ProductPropertyValueDO updateObj = BeanUtils.toBean(updateReqVO, ProductPropertyValueDO.class);
        propertyValueRepository.updateById(updateObj, getLoginUser());
    }

    @Override
    public void deletePropertyValue(String id) {
        ObjectId oid = new ObjectId(id);
        // 校验存在
        validatePropertyValueExists(oid);
        // TODO 校验是否被使用
        // 删除
        propertyValueRepository.deleteById(oid, getLoginUser());
    }

    private void validatePropertyValueExists(ObjectId id) {
        if (!propertyValueRepository.validateExists(id)) {
            throw exception(PROPERTY_VALUE_NOT_EXISTS);
        }
    }

    private void validatePropertyValueDetail(ProductPropertyValueSaveReqVO reqVO) {
        // 校验, 属性项是否存在
        ProductPropertyDO property = propertyService.getProperty(reqVO.getPropertyId());
        if (property == null) {
            throw exception(PROPERTY_NOT_EXISTS);
        }
        // 校验, 如果计量单位必须是'0', 则属性项, valueType类型必须是 '10'-文字枚举, 否则校验计量单位是否存在
        if (reqVO.getUnitId().equals(ProductUnitDO.UNIT_NULL)) {
            if (!property.getValueType().equals(ProductPropertyValueTypeEnum.ENUMS.getValue())) {
                throw exception(PROPERTY_VALUE_ONLY_ENUMS_TYPE_UNIT_ZERO);
            }
        } else {
            ProductUnitDO unit = unitService.getUnit(reqVO.getUnitId());
            if (unit == null) {
                throw exception(UNIT_NOT_EXISTS);
            }
        }
        // 校验, 属性项, 和 属性值的 valueRange, valueExpr, 是否匹配; '10'-文字枚举, '20'-数值范围, '30'-计算公式
        if (property.getValueType().equals(ProductPropertyValueTypeEnum.ENUMS.getValue())) {
            // 属性项 valueType == '10' 时, 数值范围==null, 表达式==null
            if (!(reqVO.getValueRange() == null && reqVO.getValueExpr() == null)) {
                throw exception(PROPERTY_VALUE_ENUMS_TYPE_NOT_MATCH);
            }
        } else if (property.getValueType().equals(ProductPropertyValueTypeEnum.RANGE.getValue())) {
            // 属性项 valueType == '20' 时, 数值范围!=null, 表达式==null
            if (!(reqVO.getValueRange() != null && reqVO.getValueExpr() == null)) {
                throw exception(PROPERTY_VALUE_RANGE_TYPE_NOT_MATCH);
            }
        } else if (property.getValueType().equals(ProductPropertyValueTypeEnum.COMPUTED.getValue())) {
            // 属性项 valueType == '30' 时, 数值范围==null, 表达式!=null
            if (!(reqVO.getValueRange() == null && reqVO.getValueExpr() != null)) {
                throw exception(PROPERTY_VALUE_COMPUTED_TYPE_NOT_MATCH);
            }
        } else {
            throw exception(PROPERTY_VALUE_TYPE_NOT_MATCH);
        }

        // 校验, 数值范围是否合法
        if (reqVO.getValueRange() != null) {
            Assert.notNull(reqVO.getValueRange().getMin(), "属性值范围最小值不能为空");
            Assert.notNull(reqVO.getValueRange().getMax(), "属性值范围最大值不能为空");
            Assert.notNull(reqVO.getValueRange().getDefaultValue(), "属性值范围默认值不能为空");
            
            // 校验默认值是否在范围内
            if (reqVO.getValueRange().getDefaultValue().compareTo(reqVO.getValueRange().getMin()) < 0 ||
                reqVO.getValueRange().getDefaultValue().compareTo(reqVO.getValueRange().getMax()) > 0) {
                throw exception(PROPERTY_VALUE_RANGE_INVALID);
            }
        }

        // 校验, 表达式是否合法
        if (reqVO.getValueExpr() != null) {
            Assert.notNull(reqVO.getValueExpr().getExpr(), "表达式不能为空");
            Assert.notNull(reqVO.getValueExpr().getVars(), "表达式类型不能为空");
            // 校验表达式变量是否存在
            if (reqVO.getValueExpr().getVars().size() < 1) {
                throw exception(PROPERTY_VALUE_EXPR_VARS_NOT_EXISTS);
            }
            // TODO 校验表达式是否合法
        }
    }

    @Override
    public ProductPropertyValueDO getPropertyValue(String id) {
        return propertyValueRepository.selectById(id);
    }

    @Override
    public PageResult<ProductPropertyValueDO> getPropertyValuePage(ProductPropertyValuePageReqVO pageReqVO) {
        return propertyValueRepository.selectPage(pageReqVO);
    }

    @Override
    public List<ProductPropertyValueSimpleRespVO> getPropertyValueSimpleList(ProductPropertyValueListReqVO listReqVO) {
        List<ProductPropertyValueDO> list = propertyValueRepository.selectList(listReqVO);
        return BeanUtils.toBean(list, ProductPropertyValueSimpleRespVO.class);
    }

}