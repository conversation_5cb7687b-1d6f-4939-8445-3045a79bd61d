package net.iofun.mdis.module.product.controller.admin.property.vo.property;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import net.iofun.mdis.framework.common.pojo.PageParam;

@Schema(description = "管理后台 - 产品属性项分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductPropertyPageReqVO extends PageParam {

    @Schema(description = "属性名称")
    private String name;

    @Schema(description = "英文名称")
    private String labelEn;

    @Schema(description = "分类级别")
    private Integer cateLevel;

    @Schema(description = "属性类型")
    private String attrType;

    @Schema(description = "属性值类型")
    private String valueType;

    @Schema(description = "是否显示")
    private Boolean isDisplay;

}