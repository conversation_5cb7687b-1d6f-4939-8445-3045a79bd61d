package net.iofun.mdis.module.product.dal.repository.unit;

import java.util.*;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Repository;
import net.iofun.mdis.framework.mongo.core.repository.BaseRepository;
import net.iofun.mdis.framework.security.core.LoginUser;
import com.mongodb.client.result.UpdateResult;

import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.mongo.core.query.LambdaQueryWrapperMongo;
import net.iofun.mdis.module.product.dal.dataobject.unit.ProductUnitDO;
import net.iofun.mdis.module.product.controller.admin.unit.vo.*;

/**
 * 计量单位 Repository
 *
 * <AUTHOR>
 */
@Repository
public class ProductUnitRepository extends BaseRepository<ProductUnitDO, ObjectId> {
    public ProductUnitRepository(MongoTemplate mongoOperations) {
        super(ProductUnitDO.class, mongoOperations);
    }

    public PageResult<ProductUnitDO> selectPage(ProductUnitPageReqVO reqVO) {
        LambdaQueryWrapperMongo<ProductUnitDO> queryWrapper = new LambdaQueryWrapperMongo<>(ProductUnitDO.class)
                .likeIfPresent(ProductUnitDO::getSymbol, reqVO.getSymbol())
                .likeIfPresent(ProductUnitDO::getNote, reqVO.getNote())
                .eqIfPresent(ProductUnitDO::getType, reqVO.getType())
                .eqIfPresent(ProductUnitDO::getStatus, reqVO.getStatus())
                .orderByDesc(ProductUnitDO::getId);
        return selectPage(reqVO, queryWrapper);
    }


    public ProductUnitDO selectById(String id) {
        LambdaQueryWrapperMongo<ProductUnitDO> queryWrapper = new LambdaQueryWrapperMongo<>(ProductUnitDO.class)
                .eq(ProductUnitDO::getId, id);
        return findOne(queryWrapper).orElse(null);
    }

    public boolean validateExists(ObjectId id) {
        LambdaQueryWrapperMongo<ProductUnitDO> queryWrapper = new LambdaQueryWrapperMongo<>(ProductUnitDO.class)
                .eq(ProductUnitDO::getId, id);
        return existsByQuery(queryWrapper);
    }

    public UpdateResult deleteById(ObjectId id, LoginUser loginUser) {
        LambdaQueryWrapperMongo<ProductUnitDO> queryWrapper = new LambdaQueryWrapperMongo<>(ProductUnitDO.class)
                .eq(ProductUnitDO::getId, id);
        return softDeleteByQuery(queryWrapper, loginUser);
    }

    public List<ProductUnitDO> selectList(ProductUnitListReqVO listReqVO) {
        LambdaQueryWrapperMongo<ProductUnitDO> queryWrapper = new LambdaQueryWrapperMongo<>(ProductUnitDO.class)
                .likeIfPresent(ProductUnitDO::getSymbol, listReqVO.getSymbol())
                .likeIfPresent(ProductUnitDO::getNote, listReqVO.getNote())
                .eqIfPresent(ProductUnitDO::getType, listReqVO.getType())
                .eqIfPresent(ProductUnitDO::getStatus, listReqVO.getStatus())
                .orderByAsc(ProductUnitDO::getSort);
        return selectList(queryWrapper);
    }

}