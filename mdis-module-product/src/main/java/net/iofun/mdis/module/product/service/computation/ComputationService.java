package net.iofun.mdis.module.product.service.computation;

import net.iofun.mdis.module.product.controller.admin.computation.vo.ComputationReqVO;
import net.iofun.mdis.module.product.controller.admin.computation.vo.ComputationRespVO;

/**
 * 计算服务接口
 *
 * <AUTHOR>
 */
public interface ComputationService {

    /**
     * 场景一：直接计算
     * 前端传来的变量都有明确的值，直接进行表达式计算
     *
     * @param request 计算请求
     * @return 计算结果
     */
    ComputationRespVO computeDirectly(ComputationReqVO request);

    /**
     * 场景二：补全变量后计算
     * 前端传来的部分变量没有值，需要根据业务逻辑补全变量后再计算
     *
     * @param request 计算请求
     * @return 计算结果
     */
    ComputationRespVO computeWithVariableResolution(ComputationReqVO request);
}