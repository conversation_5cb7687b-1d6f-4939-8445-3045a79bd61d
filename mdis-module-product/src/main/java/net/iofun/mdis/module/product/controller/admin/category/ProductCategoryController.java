package net.iofun.mdis.module.product.controller.admin.category;

import net.iofun.mdis.framework.common.enums.CommonStatusEnum;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import net.iofun.mdis.framework.common.pojo.PageParam;
import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.common.pojo.CommonResult;
import net.iofun.mdis.framework.common.util.object.BeanUtils;
import static net.iofun.mdis.framework.common.pojo.CommonResult.success;

import net.iofun.mdis.framework.excel.core.util.ExcelUtils;

import net.iofun.mdis.framework.apilog.core.annotation.ApiAccessLog;
import static net.iofun.mdis.framework.apilog.core.enums.OperateTypeEnum.*;

import net.iofun.mdis.module.product.controller.admin.category.vo.*;
import net.iofun.mdis.module.product.dal.dataobject.category.ProductCategoryDO;
import net.iofun.mdis.module.product.service.category.ProductCategoryService;

@Tag(name = "管理后台 - 产品分类")
@RestController
@RequestMapping("/product/category")
@Validated
public class ProductCategoryController {

    @Resource
    private ProductCategoryService categoryService;

    @PostMapping("/create")
    @Operation(summary = "创建产品分类")
    @PreAuthorize("@ss.hasPermission('product:category:create')")
    public CommonResult<String> createCategory(@Valid @RequestBody ProductCategorySaveReqVO createReqVO) {
        return success(categoryService.createCategory(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新产品分类")
    @PreAuthorize("@ss.hasPermission('product:category:update')")
    public CommonResult<Boolean> updateCategory(@Valid @RequestBody ProductCategorySaveReqVO updateReqVO) {
        categoryService.updateCategory(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除产品分类")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('product:category:delete')")
    public CommonResult<Boolean> deleteCategory(@RequestParam("id") String id) {
        categoryService.deleteCategory(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得产品分类")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('product:category:query')")
    public CommonResult<ProductCategoryRespVO> getCategory(@RequestParam("id") String id) {
        ProductCategoryDO category = categoryService.getCategory(id);
        return success(BeanUtils.toBean(category, ProductCategoryRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得产品分类分页")
    @PreAuthorize("@ss.hasPermission('product:category:query')")
    public CommonResult<PageResult<ProductCategoryRespVO>> getCategoryPage(@Valid ProductCategoryPageReqVO pageReqVO) {
        PageResult<ProductCategoryDO> pageResult = categoryService.getCategoryPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ProductCategoryRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出产品分类 Excel")
    @PreAuthorize("@ss.hasPermission('product:category:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportCategoryExcel(@Valid ProductCategoryPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ProductCategoryDO> list = categoryService.getCategoryPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "产品分类.xls", "数据", ProductCategoryRespVO.class,
                        BeanUtils.toBean(list, ProductCategoryRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得产品分类列表")
    @PreAuthorize("@ss.hasPermission('product:category:query')")
    public CommonResult<List<ProductCategoryRespVO>> getCategoryList(@Valid ProductCategoryListReqVO listReqVO) {
        List<ProductCategoryDO> list = categoryService.getCategoryList(listReqVO);
        list.sort(Comparator.comparing(ProductCategoryDO::getSort));
        return success(BeanUtils.toBean(list, ProductCategoryRespVO.class));
    }

    @GetMapping("/simple-list")
    @Operation(summary = "获得产品分类简单列表")
    @PreAuthorize("@ss.hasPermission('product:category:query')")
    public CommonResult<List<ProductCategorySimpleRespVO>> getCategorySimpleList(@Valid ProductCategoryListReqVO listReqVO) {
        List<ProductCategoryDO> list = categoryService.getCategoryList(listReqVO);
        list.sort(Comparator.comparing(ProductCategoryDO::getSort));
        return success(BeanUtils.toBean(list, ProductCategorySimpleRespVO.class));
    }

    @GetMapping("/simple-list-by-parentid-status")
    @Operation(summary = "根据父分类ID获得产品分类简单列表")
    @Parameter(name = "parentId", description = "父分类ID", required = false)
    @Parameter(name = "status", description = "分类状态", required = false)
    @PreAuthorize("@ss.hasPermission('product:category:query')")
    public CommonResult<List<ProductCategorySimpleRespVO>> getCategorySimpleListByParentIdAndStatus(
        @RequestParam("parentId") String parentId,
        @RequestParam("status") Integer status) {
        List<ProductCategoryDO> list = categoryService.getChildCategoryList(parentId, status);
        return success(BeanUtils.toBean(list, ProductCategorySimpleRespVO.class));
    }

}