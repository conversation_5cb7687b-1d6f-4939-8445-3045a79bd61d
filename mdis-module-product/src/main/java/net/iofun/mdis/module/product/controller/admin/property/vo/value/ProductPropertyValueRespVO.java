package net.iofun.mdis.module.product.controller.admin.property.vo.value;

import io.swagger.v3.oas.annotations.media.Schema;
import net.iofun.mdis.framework.common.vo.BaseVO;
import lombok.*;
import com.alibaba.excel.annotation.*;
import net.iofun.mdis.module.product.dal.dataobject.property.inner.NumberRangeType;
import net.iofun.mdis.module.product.dal.dataobject.property.inner.ExpressionType;

@Schema(description = "管理后台 - 产品属性值 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProductPropertyValueRespVO extends BaseVO {

    @Schema(description = "属性值", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("属性值")
    private String valueName;

    @Schema(description = "计量单位ID")
    @ExcelProperty("计量单位ID")
    private String unitId;

    @Schema(description = "属性值范围")
    @ExcelProperty("属性值范围")
    private NumberRangeType valueRange;

    @Schema(description = "属性值公式")
    @ExcelProperty("属性值公式")
    private ExpressionType valueExpr;

    @Schema(description = "属性项ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("属性项ID")
    private String propertyId;

    @Schema(description = "分类ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("分类ID")
    private String categoryId;

}