package net.iofun.mdis.module.product.dal.repository.category;

import java.util.*;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Repository;
import net.iofun.mdis.framework.mongo.core.repository.BaseRepository;
import net.iofun.mdis.framework.security.core.LoginUser;
import com.mongodb.client.result.UpdateResult;

import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.mongo.core.query.LambdaQueryWrapperMongo;
import net.iofun.mdis.module.product.dal.dataobject.category.ProductCategoryDO;
import net.iofun.mdis.module.product.controller.admin.category.vo.*;

/**
 * 产品分类 Repository
 *
 * <AUTHOR>
 */
@Repository
public class ProductCategoryRepository extends BaseRepository<ProductCategoryDO, ObjectId> {
    public ProductCategoryRepository(MongoTemplate mongoOperations) {
        super(ProductCategoryDO.class, mongoOperations);
    }

    public PageResult<ProductCategoryDO> selectPage(ProductCategoryPageReqVO reqVO) {
        LambdaQueryWrapperMongo<ProductCategoryDO> queryWrapper = new LambdaQueryWrapperMongo<>(ProductCategoryDO.class)
                .likeIfPresent(ProductCategoryDO::getName, reqVO.getName())
                .eqIfPresent(ProductCategoryDO::getParentId, reqVO.getParentId())
                .orderByDesc(ProductCategoryDO::getId);
        return selectPage(reqVO, queryWrapper);
    }

    public List<ProductCategoryDO> selectList(ProductCategoryListReqVO reqVO) {
        LambdaQueryWrapperMongo<ProductCategoryDO> queryWrapper = new LambdaQueryWrapperMongo<>(ProductCategoryDO.class)
                .likeIfPresent(ProductCategoryDO::getName, reqVO.getName())
                .eqIfPresent(ProductCategoryDO::getStatus, reqVO.getStatus())
                .eqIfPresent(ProductCategoryDO::getParentId, reqVO.getParentId())
                .orderByDesc(ProductCategoryDO::getId);
        return selectList(queryWrapper);
    }

    public List<ProductCategoryDO> selectListByParentId(Collection<String> parentIds) {
        LambdaQueryWrapperMongo<ProductCategoryDO> queryWrapper = new LambdaQueryWrapperMongo<>(ProductCategoryDO.class)
                .in(ProductCategoryDO::getParentId, parentIds);
        return selectList(queryWrapper);
    }

    public ProductCategoryDO selectById(String id) {
        LambdaQueryWrapperMongo<ProductCategoryDO> queryWrapper = new LambdaQueryWrapperMongo<>(ProductCategoryDO.class)
                .eq(ProductCategoryDO::getId, id);
        return findOne(queryWrapper).orElse(null);
    }

    public boolean validateExists(ObjectId id) {
        LambdaQueryWrapperMongo<ProductCategoryDO> queryWrapper = new LambdaQueryWrapperMongo<>(ProductCategoryDO.class)
                .eq(ProductCategoryDO::getId, id);
        return existsByQuery(queryWrapper);
    }

    public UpdateResult deleteById(ObjectId id, LoginUser loginUser) {
        LambdaQueryWrapperMongo<ProductCategoryDO> queryWrapper = new LambdaQueryWrapperMongo<>(ProductCategoryDO.class)
                .eq(ProductCategoryDO::getId, id);
        return softDeleteByQuery(queryWrapper, loginUser);
    }

}