package net.iofun.mdis.module.product.service.spu;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import org.bson.types.ObjectId;

import jakarta.annotation.Resource;

import net.iofun.mdis.framework.test.core.ut.BaseDbUnitTest;
import net.iofun.mdis.framework.mongo.core.query.LambdaQueryWrapperMongo;
import net.iofun.mdis.framework.tenant.config.TenantProperties;
import net.iofun.mdis.framework.tenant.core.db.TenantMongoDBHandler;
import net.iofun.mdis.framework.security.core.LoginUser;
import net.iofun.mdis.framework.tenant.core.context.TenantContextHolder;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.Authentication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.context.annotation.ComponentScan;
import com.mongodb.client.result.UpdateResult;

import net.iofun.mdis.module.product.controller.admin.spu.vo.*;
import net.iofun.mdis.module.product.dal.dataobject.spu.ProductSpuDO;
import net.iofun.mdis.module.product.dal.repository.spu.ProductSpuRepository;
import net.iofun.mdis.framework.common.pojo.PageResult;

import jakarta.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.math.RoundingMode;

import static net.iofun.mdis.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;
import static cn.hutool.core.util.RandomUtil.*;
import static net.iofun.mdis.module.product.enums.ErrorCodeConstants.*;
import static net.iofun.mdis.framework.test.core.util.AssertUtils.*;
import static net.iofun.mdis.framework.test.core.util.RandomUtils.*;
import static net.iofun.mdis.framework.common.util.date.LocalDateTimeUtils.*;
import static net.iofun.mdis.framework.common.util.object.ObjectUtils.*;
import static net.iofun.mdis.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * {@link ProductSpuServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(ProductSpuServiceImpl.class)
@ExtendWith(MockitoExtension.class)
public class ProductSpuServiceImplTest extends BaseDbUnitTest {

    @Resource
    private ProductSpuServiceImpl spuService;

    @Mock
    private ProductSpuRepository spuRepository;

    @Mock
    private TenantProperties tenantProperties;
    
    // 用于模拟存储的列表
    private List<ProductSpuDO> mockSpus = new ArrayList<>();

    @BeforeEach
    void setUp() {
        // 设置模拟登录用户和租户上下文
        setupMockUser();
        
        // 获取集合名并配置到租户属性中
        String collectionName = ProductSpuDO.class.getAnnotation(Document.class).collection();
        when(tenantProperties.getIgnoreTables()).thenReturn(Collections.singleton(collectionName));
        
        // 清空模拟数据库
        mockSpus.clear();
        
        // 配置repository模拟行为
        setupMockRepository();
    }
    
    private void setupMockRepository() {
        // 模拟insert方法
        when(spuRepository.insert(any(ProductSpuDO.class), any(LoginUser.class))).thenAnswer(invocation -> {
            ProductSpuDO entity = invocation.getArgument(0);
            if (entity.getId() == null) {
                entity.setId(new ObjectId());
            }
            mockSpus.add(entity);
            return entity;
        });
        
        // 模拟selectById方法 - 修复String和ObjectId比较逻辑
        when(spuRepository.selectById(any(String.class))).thenAnswer(invocation -> {
            String id = invocation.getArgument(0);
            return mockSpus.stream()
                .filter(r -> r.getId().toHexString().equals(id))
                .findFirst()
                .orElse(null);
        });
        
        // 模拟validateExists方法
        when(spuRepository.validateExists(any(ObjectId.class))).thenAnswer(invocation -> {
            ObjectId id = invocation.getArgument(0);
            return mockSpus.stream()
                .anyMatch(r -> r.getId().equals(id));
        });
        
        // 模拟update方法 - 返回UpdateResult
        when(spuRepository.updateById(any(ProductSpuDO.class), any(LoginUser.class))).thenAnswer(invocation -> {
            ProductSpuDO newEntity = invocation.getArgument(0);
            boolean updated = false;
            for (int i = 0; i < mockSpus.size(); i++) {
                if (mockSpus.get(i).getId().equals(newEntity.getId())) {
                    mockSpus.set(i, newEntity);
                    updated = true;
                    break;
                }
            }
            UpdateResult result = mock(UpdateResult.class);
            when(result.getMatchedCount()).thenReturn(updated ? 1L : 0L);
            when(result.getModifiedCount()).thenReturn(updated ? 1L : 0L);
            return result;
        });
        
        // 模拟deleteById方法 - 返回UpdateResult
        when(spuRepository.deleteById(any(ObjectId.class), any(LoginUser.class))).thenAnswer(invocation -> {
            ObjectId id = invocation.getArgument(0);
            boolean removed = mockSpus.removeIf(r -> r.getId().equals(id));
            UpdateResult result = mock(UpdateResult.class);
            when(result.getMatchedCount()).thenReturn(removed ? 1L : 0L);
            when(result.getModifiedCount()).thenReturn(removed ? 1L : 0L);
            return result;
        });
        
        // 模拟分页查询
        when(spuRepository.selectPage(any(ProductSpuPageReqVO.class))).thenAnswer(invocation -> {
            // 简单返回所有数据，实际项目中应该根据查询条件过滤
            return new PageResult<ProductSpuDO>(mockSpus, (long) mockSpus.size());
        });
        
        // 模拟deleteByQuery方法
        when(spuRepository.deleteByQuery(any())).thenAnswer(invocation -> {
            int size = mockSpus.size();
            mockSpus.clear();
            return size;
        });
    }

    @AfterEach
    public void tearDown() {
        // 清理安全上下文和租户上下文
        SecurityContextHolder.clearContext();
        TenantContextHolder.clear();
        // 清空模拟数据
        mockSpus.clear();
    }

    // 在每个测试方法前设置模拟登录用户
    private void setupMockUser() {
        // 设置模拟登录用户
        LoginUser loginUser = new LoginUser();
        loginUser.setId("mock-user-id");
        Authentication authentication = new UsernamePasswordAuthenticationToken(
                loginUser, null, Collections.emptyList());
        SecurityContextHolder.getContext().setAuthentication(authentication);
        
        // 设置具体的租户ID
        TenantContextHolder.setTenantId("mock-tenant-id");
        // 设置忽略租户，以避免MongoDB租户相关问题
        TenantContextHolder.setIgnore(true);
    }

    @Test
    public void testCreateSpu_success() {
        // 准备参数
        ProductSpuSaveReqVO createReqVO = randomPojo(ProductSpuSaveReqVO.class, o -> {
            o.setId(null);
        });

        // 调用
        String spuId = spuService.createSpu(createReqVO);
        
        // 断言
        assertNotNull(spuId);
        // 校验记录的属性是否正确
        ProductSpuDO spu = spuRepository.selectById(spuId);
        assertNotNull(spu);
        assertPojoEquals(createReqVO, spu, "id");
    }

    @Test
    public void testUpdateSpu_success() {
        // mock 数据
        ProductSpuDO dbSpu = randomPojo(ProductSpuDO.class, o -> {
        });
        spuRepository.insert(dbSpu, getLoginUser());
        
        // 准备参数
        ProductSpuSaveReqVO updateReqVO = randomPojo(ProductSpuSaveReqVO.class, o -> {
            o.setId(dbSpu.getId()); // 设置更新的 ID
        });

        // 调用
        spuService.updateSpu(updateReqVO);
        
        // 校验是否更新正确
        ProductSpuDO spu = spuRepository.selectById(updateReqVO.getId().toHexString());
        assertNotNull(spu);
        assertPojoEquals(updateReqVO, spu);
    }

    @Test
    public void testUpdateSpu_notExists() {
        // 准备参数
        ProductSpuSaveReqVO updateReqVO = randomPojo(ProductSpuSaveReqVO.class, o -> {
            o.setId(new ObjectId());
        });

        // 调用, 并断言异常
        assertServiceException(() -> spuService.updateSpu(updateReqVO), SPU_NOT_EXISTS);
    }

    @Test
    public void testDeleteSpu_success() {
        // mock 数据
        ProductSpuDO dbSpu = randomPojo(ProductSpuDO.class, o -> {
        });
        spuRepository.insert(dbSpu, getLoginUser());
        
        // 准备参数
        String id = dbSpu.getId().toHexString();

        // 调用
        spuService.deleteSpu(id);
        
        // 校验数据不存在了
        assertNull(spuRepository.selectById(id));
    }

    @Test
    public void testDeleteSpu_notExists() {
        // 准备参数
        ObjectId id = new ObjectId();
        
        // 调用, 并断言异常
        assertServiceException(() -> spuService.deleteSpu(id.toHexString()), SPU_NOT_EXISTS);
    }

    @Test
    public void testGetSpuPage() {
        // 简化测试，只创建一条记录
        ProductSpuDO dbSpu = randomPojo(ProductSpuDO.class, o -> {
            
            // 清除可能影响查询的字段
            o.setName("test-name");
            o.setLabelEn("test-labelEn");
            o.setType("test-type");
            o.setStatus(0);
            o.setCode("test-code");
        });
        spuRepository.insert(dbSpu, getLoginUser());
        
        // 准备参数 - 使用最简单的空查询条件
        ProductSpuPageReqVO reqVO = new ProductSpuPageReqVO();
        
        // 调用
        PageResult<ProductSpuDO> pageResult = spuService.getSpuPage(reqVO);
        
        // 断言 - 只检查是否能找到我们创建的记录
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        assertPojoEquals(dbSpu, pageResult.getList().get(0));
    }

}