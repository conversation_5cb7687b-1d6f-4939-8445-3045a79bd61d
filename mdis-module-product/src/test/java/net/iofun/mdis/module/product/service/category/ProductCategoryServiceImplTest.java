package net.iofun.mdis.module.product.service.category;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import org.bson.types.ObjectId;

import jakarta.annotation.Resource;

import net.iofun.mdis.framework.test.core.ut.BaseDbUnitTest;
import net.iofun.mdis.framework.tenant.config.TenantProperties;
import net.iofun.mdis.framework.security.core.LoginUser;
import net.iofun.mdis.framework.tenant.core.context.TenantContextHolder;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.Authentication;
import org.springframework.data.mongodb.core.mapping.Document;
import com.mongodb.client.result.UpdateResult;

import net.iofun.mdis.module.product.controller.admin.category.vo.*;
import net.iofun.mdis.module.product.dal.dataobject.category.ProductCategoryDO;
import net.iofun.mdis.module.product.dal.repository.category.ProductCategoryRepository;
import net.iofun.mdis.framework.common.pojo.PageResult;

import org.springframework.context.annotation.Import;
import java.util.*;

import static net.iofun.mdis.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;
import static net.iofun.mdis.module.product.enums.ErrorCodeConstants.*;
import static net.iofun.mdis.framework.test.core.util.AssertUtils.*;
import static net.iofun.mdis.framework.test.core.util.RandomUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * {@link ProductCategoryServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(ProductCategoryServiceImpl.class)
@ExtendWith(MockitoExtension.class)
public class ProductCategoryServiceImplTest extends BaseDbUnitTest {

    @Resource
    private ProductCategoryServiceImpl categoryService;

    @Mock
    private ProductCategoryRepository categoryRepository;

    @Mock
    private TenantProperties tenantProperties;
    
    // 用于模拟存储的列表
    private List<ProductCategoryDO> mockCategorys = new ArrayList<>();


    @BeforeEach
    void setUp() {
        // 设置模拟登录用户和租户上下文
        setupMockUser();
        
        // 获取集合名并配置到租户属性中
        String collectionName = ProductCategoryDO.class.getAnnotation(Document.class).collection();
        when(tenantProperties.getIgnoreTables()).thenReturn(Collections.singleton(collectionName));
        
        // 清空模拟数据库
        mockCategorys.clear();
        
        // 配置repository模拟行为
        setupMockRepository();
    }
    
    private void setupMockRepository() {
        // 模拟insert方法
        when(categoryRepository.insert(any(ProductCategoryDO.class), any(LoginUser.class))).thenAnswer(invocation -> {
            ProductCategoryDO entity = invocation.getArgument(0);
            if (entity.getId() == null) {
                entity.setId(new ObjectId());
            }
            mockCategorys.add(entity);
            return entity;
        });
        
        // 模拟selectById方法 - 修复String和ObjectId比较逻辑
        when(categoryRepository.selectById(any(String.class))).thenAnswer(invocation -> {
            String id = invocation.getArgument(0);
            return mockCategorys.stream()
                .filter(r -> r.getId().toHexString().equals(id))
                .findFirst()
                .orElse(null);
        });
        
        // 模拟validateExists方法
        when(categoryRepository.validateExists(any(ObjectId.class))).thenAnswer(invocation -> {
            ObjectId id = invocation.getArgument(0);
            return mockCategorys.stream()
                .anyMatch(r -> r.getId().equals(id));
        });
        
        // 模拟update方法 - 返回UpdateResult
        when(categoryRepository.updateById(any(ProductCategoryDO.class), any(LoginUser.class))).thenAnswer(invocation -> {
            ProductCategoryDO newEntity = invocation.getArgument(0);
            boolean updated = false;
            for (int i = 0; i < mockCategorys.size(); i++) {
                if (mockCategorys.get(i).getId().equals(newEntity.getId())) {
                    mockCategorys.set(i, newEntity);
                    updated = true;
                    break;
                }
            }
            UpdateResult result = mock(UpdateResult.class);
            when(result.getMatchedCount()).thenReturn(updated ? 1L : 0L);
            when(result.getModifiedCount()).thenReturn(updated ? 1L : 0L);
            return result;
        });
        
        // 模拟deleteById方法 - 返回UpdateResult
        when(categoryRepository.deleteById(any(ObjectId.class), any(LoginUser.class))).thenAnswer(invocation -> {
            ObjectId id = invocation.getArgument(0);
            boolean removed = mockCategorys.removeIf(r -> r.getId().equals(id));
            UpdateResult result = mock(UpdateResult.class);
            when(result.getMatchedCount()).thenReturn(removed ? 1L : 0L);
            when(result.getModifiedCount()).thenReturn(removed ? 1L : 0L);
            return result;
        });
        
        // 模拟分页查询
        when(categoryRepository.selectPage(any(ProductCategoryPageReqVO.class))).thenAnswer(invocation -> {
            // 简单返回所有数据，实际项目中应该根据查询条件过滤
            return new PageResult<ProductCategoryDO>(mockCategorys, (long) mockCategorys.size());
        });
        
        // 模拟deleteByQuery方法
        when(categoryRepository.deleteByQuery(any())).thenAnswer(invocation -> {
            int size = mockCategorys.size();
            mockCategorys.clear();
            return size;
        });
    }

    @AfterEach
    public void tearDown() {
        // 清理安全上下文和租户上下文
        SecurityContextHolder.clearContext();
        TenantContextHolder.clear();
        // 清空模拟数据
        mockCategorys.clear();
    }

    // 在每个测试方法前设置模拟登录用户
    private void setupMockUser() {
        // 设置模拟登录用户
        LoginUser loginUser = new LoginUser();
        loginUser.setId("mock-user-id");
        Authentication authentication = new UsernamePasswordAuthenticationToken(
                loginUser, null, Collections.emptyList());
        SecurityContextHolder.getContext().setAuthentication(authentication);
        
        // 设置具体的租户ID
        TenantContextHolder.setTenantId("mock-tenant-id");
        // 设置忽略租户，以避免MongoDB租户相关问题
        TenantContextHolder.setIgnore(true);
    }

    @Test
    public void testCreateCategory_success() {
        // 准备参数
        ProductCategorySaveReqVO createReqVO = randomPojo(ProductCategorySaveReqVO.class, o -> {
            o.setId(null);
        });

        // 调用
        String categoryId = categoryService.createCategory(createReqVO);
        
        // 断言
        assertNotNull(categoryId);
        // 校验记录的属性是否正确
        ProductCategoryDO category = categoryRepository.selectById(categoryId);
        assertNotNull(category);
        assertPojoEquals(createReqVO, category, "id");
    }

    @Test
    public void testUpdateCategory_success() {
        // mock 数据
        ProductCategoryDO dbCategory = randomPojo(ProductCategoryDO.class, o -> {
        });
        categoryRepository.insert(dbCategory, getLoginUser());
        
        // 准备参数
        ProductCategorySaveReqVO updateReqVO = randomPojo(ProductCategorySaveReqVO.class, o -> {
            o.setId(dbCategory.getId()); // 设置更新的 ID
        });

        // 调用
        categoryService.updateCategory(updateReqVO);
        
        // 校验是否更新正确
        ProductCategoryDO category = categoryRepository.selectById(updateReqVO.getId().toHexString());
        assertNotNull(category);
        assertPojoEquals(updateReqVO, category);
    }

    @Test
    public void testUpdateCategory_notExists() {
        // 准备参数
        ProductCategorySaveReqVO updateReqVO = randomPojo(ProductCategorySaveReqVO.class, o -> {
            o.setId(new ObjectId());
        });

        // 调用, 并断言异常
        assertServiceException(() -> categoryService.updateCategory(updateReqVO), CATEGORY_NOT_EXISTS);
    }

    @Test
    public void testDeleteCategory_success() {
        // mock 数据
        ProductCategoryDO dbCategory = randomPojo(ProductCategoryDO.class, o -> {
        });
        categoryRepository.insert(dbCategory, getLoginUser());
        
        // 准备参数
        String id = dbCategory.getId().toHexString();

        // 调用
        categoryService.deleteCategory(id);
        
        // 校验数据不存在了
        assertNull(categoryRepository.selectById(id));
    }

    @Test
    public void testDeleteCategory_notExists() {
        // 准备参数
        ObjectId id = new ObjectId();
        
        // 调用, 并断言异常
        assertServiceException(() -> categoryService.deleteCategory(id.toHexString()), CATEGORY_NOT_EXISTS);
    }

    @Test
    public void testGetCategoryPage() {
        // 简化测试，只创建一条记录
        ProductCategoryDO dbCategory = randomPojo(ProductCategoryDO.class, o -> {
            
            // 清除可能影响查询的字段
            o.setName("test-name");
            o.setParentId("test-parentId");
            o.setStatus(0);
        });
        categoryRepository.insert(dbCategory, getLoginUser());
        
        // 准备参数 - 使用最简单的空查询条件
        ProductCategoryPageReqVO reqVO = new ProductCategoryPageReqVO();
        
        // 调用
        PageResult<ProductCategoryDO> pageResult = categoryService.getCategoryPage(reqVO);
        
        // 断言 - 只检查是否能找到我们创建的记录
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        assertPojoEquals(dbCategory, pageResult.getList().get(0));
    }

}