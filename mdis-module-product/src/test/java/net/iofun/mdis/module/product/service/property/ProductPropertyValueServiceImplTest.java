package net.iofun.mdis.module.product.service.property;

import net.iofun.mdis.module.product.controller.admin.property.vo.value.ProductPropertyValuePageReqVO;
import net.iofun.mdis.module.product.controller.admin.property.vo.value.ProductPropertyValueSaveReqVO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import org.bson.types.ObjectId;

import jakarta.annotation.Resource;

import net.iofun.mdis.framework.test.core.ut.BaseDbUnitTest;
import net.iofun.mdis.framework.tenant.config.TenantProperties;
import net.iofun.mdis.framework.security.core.LoginUser;
import net.iofun.mdis.framework.tenant.core.context.TenantContextHolder;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.Authentication;
import org.springframework.data.mongodb.core.mapping.Document;
import com.mongodb.client.result.UpdateResult;

import net.iofun.mdis.module.product.dal.dataobject.property.ProductPropertyValueDO;
import net.iofun.mdis.module.product.dal.repository.property.ProductPropertyValueRepository;
import net.iofun.mdis.framework.common.pojo.PageResult;

import org.springframework.context.annotation.Import;
import java.util.*;

import static net.iofun.mdis.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;
import static net.iofun.mdis.framework.test.core.util.AssertUtils.*;
import static net.iofun.mdis.framework.test.core.util.RandomUtils.*;
import static net.iofun.mdis.module.product.enums.ErrorCodeConstants.PROPERTY_VALUE_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * {@link ProductPropertyValueServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(ProductPropertyValueServiceImpl.class)
@ExtendWith(MockitoExtension.class)
public class ProductPropertyValueServiceImplTest extends BaseDbUnitTest {

    @Resource
    private ProductPropertyValueServiceImpl propertyValueService;

    @Mock
    private ProductPropertyValueRepository propertyValueRepository;

    @Mock
    private TenantProperties tenantProperties;
    
    // 用于模拟存储的列表
    private List<ProductPropertyValueDO> mockPropertyValues = new ArrayList<>();

    @BeforeEach
    void setUp() {
        // 设置模拟登录用户和租户上下文
        setupMockUser();
        
        // 获取集合名并配置到租户属性中
        String collectionName = ProductPropertyValueDO.class.getAnnotation(Document.class).collection();
        when(tenantProperties.getIgnoreTables()).thenReturn(Collections.singleton(collectionName));
        
        // 清空模拟数据库
        mockPropertyValues.clear();
        
        // 配置repository模拟行为
        setupMockRepository();
    }
    
    private void setupMockRepository() {
        // 模拟insert方法
        when(propertyValueRepository.insert(any(ProductPropertyValueDO.class), any(LoginUser.class))).thenAnswer(invocation -> {
            ProductPropertyValueDO entity = invocation.getArgument(0);
            if (entity.getId() == null) {
                entity.setId(new ObjectId());
            }
            mockPropertyValues.add(entity);
            return entity;
        });
        
        // 模拟selectById方法 - 修复String和ObjectId比较逻辑
        when(propertyValueRepository.selectById(any(String.class))).thenAnswer(invocation -> {
            String id = invocation.getArgument(0);
            return mockPropertyValues.stream()
                .filter(r -> r.getId().toHexString().equals(id))
                .findFirst()
                .orElse(null);
        });
        
        // 模拟validateExists方法
        when(propertyValueRepository.validateExists(any(ObjectId.class))).thenAnswer(invocation -> {
            ObjectId id = invocation.getArgument(0);
            return mockPropertyValues.stream()
                .anyMatch(r -> r.getId().equals(id));
        });
        
        // 模拟update方法 - 返回UpdateResult
        when(propertyValueRepository.updateById(any(ProductPropertyValueDO.class), any(LoginUser.class))).thenAnswer(invocation -> {
            ProductPropertyValueDO newEntity = invocation.getArgument(0);
            boolean updated = false;
            for (int i = 0; i < mockPropertyValues.size(); i++) {
                if (mockPropertyValues.get(i).getId().equals(newEntity.getId())) {
                    mockPropertyValues.set(i, newEntity);
                    updated = true;
                    break;
                }
            }
            UpdateResult result = mock(UpdateResult.class);
            when(result.getMatchedCount()).thenReturn(updated ? 1L : 0L);
            when(result.getModifiedCount()).thenReturn(updated ? 1L : 0L);
            return result;
        });
        
        // 模拟deleteById方法 - 返回UpdateResult
        when(propertyValueRepository.deleteById(any(ObjectId.class), any(LoginUser.class))).thenAnswer(invocation -> {
            ObjectId id = invocation.getArgument(0);
            boolean removed = mockPropertyValues.removeIf(r -> r.getId().equals(id));
            UpdateResult result = mock(UpdateResult.class);
            when(result.getMatchedCount()).thenReturn(removed ? 1L : 0L);
            when(result.getModifiedCount()).thenReturn(removed ? 1L : 0L);
            return result;
        });
        
        // 模拟分页查询
        when(propertyValueRepository.selectPage(any(ProductPropertyValuePageReqVO.class))).thenAnswer(invocation -> {
            // 简单返回所有数据，实际项目中应该根据查询条件过滤
            return new PageResult<ProductPropertyValueDO>(mockPropertyValues, (long) mockPropertyValues.size());
        });
        
        // 模拟deleteByQuery方法
        when(propertyValueRepository.deleteByQuery(any())).thenAnswer(invocation -> {
            int size = mockPropertyValues.size();
            mockPropertyValues.clear();
            return size;
        });
    }

    @AfterEach
    public void tearDown() {
        // 清理安全上下文和租户上下文
        SecurityContextHolder.clearContext();
        TenantContextHolder.clear();
        // 清空模拟数据
        mockPropertyValues.clear();
    }

    // 在每个测试方法前设置模拟登录用户
    private void setupMockUser() {
        // 设置模拟登录用户
        LoginUser loginUser = new LoginUser();
        loginUser.setId("mock-user-id");
        Authentication authentication = new UsernamePasswordAuthenticationToken(
                loginUser, null, Collections.emptyList());
        SecurityContextHolder.getContext().setAuthentication(authentication);
        
        // 设置具体的租户ID
        TenantContextHolder.setTenantId("mock-tenant-id");
        // 设置忽略租户，以避免MongoDB租户相关问题
        TenantContextHolder.setIgnore(true);
    }

    @Test
    public void testCreatePropertyValue_success() {
        // 准备参数
        ProductPropertyValueSaveReqVO createReqVO = randomPojo(ProductPropertyValueSaveReqVO.class, o -> {
            o.setId(null);
        });

        // 调用
        String propertyValueId = propertyValueService.createPropertyValue(createReqVO);
        
        // 断言
        assertNotNull(propertyValueId);
        // 校验记录的属性是否正确
        ProductPropertyValueDO propertyValue = propertyValueRepository.selectById(propertyValueId);
        assertNotNull(propertyValue);
        assertPojoEquals(createReqVO, propertyValue, "id");
    }

    @Test
    public void testUpdatePropertyValue_success() {
        // mock 数据
        ProductPropertyValueDO dbPropertyValue = randomPojo(ProductPropertyValueDO.class, o -> {
        });
        propertyValueRepository.insert(dbPropertyValue, getLoginUser());
        
        // 准备参数
        ProductPropertyValueSaveReqVO updateReqVO = randomPojo(ProductPropertyValueSaveReqVO.class, o -> {
            o.setId(dbPropertyValue.getId()); // 设置更新的 ID
        });

        // 调用
        propertyValueService.updatePropertyValue(updateReqVO);
        
        // 校验是否更新正确
        ProductPropertyValueDO propertyValue = propertyValueRepository.selectById(updateReqVO.getId().toHexString());
        assertNotNull(propertyValue);
        assertPojoEquals(updateReqVO, propertyValue);
    }

    @Test
    public void testUpdatePropertyValue_notExists() {
        // 准备参数
        ProductPropertyValueSaveReqVO updateReqVO = randomPojo(ProductPropertyValueSaveReqVO.class, o -> {
            o.setId(new ObjectId());
        });

        // 调用, 并断言异常
        assertServiceException(() -> propertyValueService.updatePropertyValue(updateReqVO), PROPERTY_VALUE_NOT_EXISTS);
    }

    @Test
    public void testDeletePropertyValue_success() {
        // mock 数据
        ProductPropertyValueDO dbPropertyValue = randomPojo(ProductPropertyValueDO.class, o -> {
        });
        propertyValueRepository.insert(dbPropertyValue, getLoginUser());
        
        // 准备参数
        String id = dbPropertyValue.getId().toHexString();

        // 调用
        propertyValueService.deletePropertyValue(id);
        
        // 校验数据不存在了
        assertNull(propertyValueRepository.selectById(id));
    }

    @Test
    public void testDeletePropertyValue_notExists() {
        // 准备参数
        ObjectId id = new ObjectId();
        
        // 调用, 并断言异常
        assertServiceException(() -> propertyValueService.deletePropertyValue(id.toHexString()), PROPERTY_VALUE_NOT_EXISTS);
    }

    @Test
    public void testGetPropertyValuePage() {
        // 简化测试，只创建一条记录
        ProductPropertyValueDO dbPropertyValue = randomPojo(ProductPropertyValueDO.class, o -> {
            
            // 清除可能影响查询的字段
            o.setValueName("test-valueName");
            o.setPropertyId("test-propertyId");
            o.setCategoryId("test-categoryId");
        });
        propertyValueRepository.insert(dbPropertyValue, getLoginUser());
        
        // 准备参数 - 使用最简单的空查询条件
        ProductPropertyValuePageReqVO reqVO = new ProductPropertyValuePageReqVO();
        
        // 调用
        PageResult<ProductPropertyValueDO> pageResult = propertyValueService.getPropertyValuePage(reqVO);
        
        // 断言 - 只检查是否能找到我们创建的记录
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        assertPojoEquals(dbPropertyValue, pageResult.getList().get(0));
    }

}