package net.iofun.mdis.module.product.service.unit;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import org.bson.types.ObjectId;

import jakarta.annotation.Resource;

import net.iofun.mdis.framework.test.core.ut.BaseDbUnitTest;
import net.iofun.mdis.framework.mongo.core.query.LambdaQueryWrapperMongo;
import net.iofun.mdis.framework.tenant.config.TenantProperties;
import net.iofun.mdis.framework.tenant.core.db.TenantMongoDBHandler;
import net.iofun.mdis.framework.security.core.LoginUser;
import net.iofun.mdis.framework.tenant.core.context.TenantContextHolder;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.Authentication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.context.annotation.ComponentScan;
import com.mongodb.client.result.UpdateResult;

import net.iofun.mdis.module.product.controller.admin.unit.vo.*;
import net.iofun.mdis.module.product.dal.dataobject.unit.ProductUnitDO;
import net.iofun.mdis.module.product.dal.repository.unit.ProductUnitRepository;
import net.iofun.mdis.framework.common.pojo.PageResult;

import jakarta.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.math.RoundingMode;

import static net.iofun.mdis.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;
import static cn.hutool.core.util.RandomUtil.*;
import static net.iofun.mdis.module.product.enums.ErrorCodeConstants.*;
import static net.iofun.mdis.framework.test.core.util.AssertUtils.*;
import static net.iofun.mdis.framework.test.core.util.RandomUtils.*;
import static net.iofun.mdis.framework.common.util.date.LocalDateTimeUtils.*;
import static net.iofun.mdis.framework.common.util.object.ObjectUtils.*;
import static net.iofun.mdis.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * {@link ProductUnitServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(ProductUnitServiceImpl.class)
@ExtendWith(MockitoExtension.class)
public class ProductUnitServiceImplTest extends BaseDbUnitTest {

    @Resource
    private ProductUnitServiceImpl unitService;

    @Mock
    private ProductUnitRepository unitRepository;

    @Mock
    private TenantProperties tenantProperties;
    
    // 用于模拟存储的列表
    private List<ProductUnitDO> mockUnits = new ArrayList<>();

    @BeforeEach
    void setUp() {
        // 设置模拟登录用户和租户上下文
        setupMockUser();
        
        // 获取集合名并配置到租户属性中
        String collectionName = ProductUnitDO.class.getAnnotation(Document.class).collection();
        when(tenantProperties.getIgnoreTables()).thenReturn(Collections.singleton(collectionName));
        
        // 清空模拟数据库
        mockUnits.clear();
        
        // 配置repository模拟行为
        setupMockRepository();
    }
    
    private void setupMockRepository() {
        // 模拟insert方法
        when(unitRepository.insert(any(ProductUnitDO.class), any(LoginUser.class))).thenAnswer(invocation -> {
            ProductUnitDO entity = invocation.getArgument(0);
            if (entity.getId() == null) {
                entity.setId(new ObjectId());
            }
            mockUnits.add(entity);
            return entity;
        });
        
        // 模拟selectById方法 - 修复String和ObjectId比较逻辑
        when(unitRepository.selectById(any(String.class))).thenAnswer(invocation -> {
            String id = invocation.getArgument(0);
            return mockUnits.stream()
                .filter(r -> r.getId().toHexString().equals(id))
                .findFirst()
                .orElse(null);
        });
        
        // 模拟validateExists方法
        when(unitRepository.validateExists(any(ObjectId.class))).thenAnswer(invocation -> {
            ObjectId id = invocation.getArgument(0);
            return mockUnits.stream()
                .anyMatch(r -> r.getId().equals(id));
        });
        
        // 模拟update方法 - 返回UpdateResult
        when(unitRepository.updateById(any(ProductUnitDO.class), any(LoginUser.class))).thenAnswer(invocation -> {
            ProductUnitDO newEntity = invocation.getArgument(0);
            boolean updated = false;
            for (int i = 0; i < mockUnits.size(); i++) {
                if (mockUnits.get(i).getId().equals(newEntity.getId())) {
                    mockUnits.set(i, newEntity);
                    updated = true;
                    break;
                }
            }
            UpdateResult result = mock(UpdateResult.class);
            when(result.getMatchedCount()).thenReturn(updated ? 1L : 0L);
            when(result.getModifiedCount()).thenReturn(updated ? 1L : 0L);
            return result;
        });
        
        // 模拟deleteById方法 - 返回UpdateResult
        when(unitRepository.deleteById(any(ObjectId.class), any(LoginUser.class))).thenAnswer(invocation -> {
            ObjectId id = invocation.getArgument(0);
            boolean removed = mockUnits.removeIf(r -> r.getId().equals(id));
            UpdateResult result = mock(UpdateResult.class);
            when(result.getMatchedCount()).thenReturn(removed ? 1L : 0L);
            when(result.getModifiedCount()).thenReturn(removed ? 1L : 0L);
            return result;
        });
        
        // 模拟分页查询
        when(unitRepository.selectPage(any(ProductUnitPageReqVO.class))).thenAnswer(invocation -> {
            // 简单返回所有数据，实际项目中应该根据查询条件过滤
            return new PageResult<ProductUnitDO>(mockUnits, (long) mockUnits.size());
        });
        
        // 模拟deleteByQuery方法
        when(unitRepository.deleteByQuery(any())).thenAnswer(invocation -> {
            int size = mockUnits.size();
            mockUnits.clear();
            return size;
        });
    }

    @AfterEach
    public void tearDown() {
        // 清理安全上下文和租户上下文
        SecurityContextHolder.clearContext();
        TenantContextHolder.clear();
        // 清空模拟数据
        mockUnits.clear();
    }

    // 在每个测试方法前设置模拟登录用户
    private void setupMockUser() {
        // 设置模拟登录用户
        LoginUser loginUser = new LoginUser();
        loginUser.setId("mock-user-id");
        Authentication authentication = new UsernamePasswordAuthenticationToken(
                loginUser, null, Collections.emptyList());
        SecurityContextHolder.getContext().setAuthentication(authentication);
        
        // 设置具体的租户ID
        TenantContextHolder.setTenantId("mock-tenant-id");
        // 设置忽略租户，以避免MongoDB租户相关问题
        TenantContextHolder.setIgnore(true);
    }

    @Test
    public void testCreateUnit_success() {
        // 准备参数
        ProductUnitSaveReqVO createReqVO = randomPojo(ProductUnitSaveReqVO.class, o -> {
            o.setId(null);
        });

        // 调用
        String unitId = unitService.createUnit(createReqVO);
        
        // 断言
        assertNotNull(unitId);
        // 校验记录的属性是否正确
        ProductUnitDO unit = unitRepository.selectById(unitId);
        assertNotNull(unit);
        assertPojoEquals(createReqVO, unit, "id");
    }

    @Test
    public void testUpdateUnit_success() {
        // mock 数据
        ProductUnitDO dbUnit = randomPojo(ProductUnitDO.class, o -> {
        });
        unitRepository.insert(dbUnit, getLoginUser());
        
        // 准备参数
        ProductUnitSaveReqVO updateReqVO = randomPojo(ProductUnitSaveReqVO.class, o -> {
            o.setId(dbUnit.getId()); // 设置更新的 ID
        });

        // 调用
        unitService.updateUnit(updateReqVO);
        
        // 校验是否更新正确
        ProductUnitDO unit = unitRepository.selectById(updateReqVO.getId().toHexString());
        assertNotNull(unit);
        assertPojoEquals(updateReqVO, unit);
    }

    @Test
    public void testUpdateUnit_notExists() {
        // 准备参数
        ProductUnitSaveReqVO updateReqVO = randomPojo(ProductUnitSaveReqVO.class, o -> {
            o.setId(new ObjectId());
        });

        // 调用, 并断言异常
        assertServiceException(() -> unitService.updateUnit(updateReqVO), UNIT_NOT_EXISTS);
    }

    @Test
    public void testDeleteUnit_success() {
        // mock 数据
        ProductUnitDO dbUnit = randomPojo(ProductUnitDO.class, o -> {
        });
        unitRepository.insert(dbUnit, getLoginUser());
        
        // 准备参数
        String id = dbUnit.getId().toHexString();

        // 调用
        unitService.deleteUnit(id);
        
        // 校验数据不存在了
        assertNull(unitRepository.selectById(id));
    }

    @Test
    public void testDeleteUnit_notExists() {
        // 准备参数
        ObjectId id = new ObjectId();
        
        // 调用, 并断言异常
        assertServiceException(() -> unitService.deleteUnit(id.toHexString()), UNIT_NOT_EXISTS);
    }

    @Test
    public void testGetUnitPage() {
        // 简化测试，只创建一条记录
        ProductUnitDO dbUnit = randomPojo(ProductUnitDO.class, o -> {
            
            // 清除可能影响查询的字段
            o.setSymbol("test-symbol");
            o.setNote("test-note");
            o.setType("test-type");
            o.setStatus(0);
        });
        unitRepository.insert(dbUnit, getLoginUser());
        
        // 准备参数 - 使用最简单的空查询条件
        ProductUnitPageReqVO reqVO = new ProductUnitPageReqVO();
        
        // 调用
        PageResult<ProductUnitDO> pageResult = unitService.getUnitPage(reqVO);
        
        // 断言 - 只检查是否能找到我们创建的记录
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        assertPojoEquals(dbUnit, pageResult.getList().get(0));
    }

}