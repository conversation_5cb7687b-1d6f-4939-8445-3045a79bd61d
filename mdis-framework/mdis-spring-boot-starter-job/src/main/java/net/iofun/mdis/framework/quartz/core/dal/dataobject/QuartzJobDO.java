package net.iofun.mdis.framework.quartz.core.dal.dataobject;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import net.iofun.mdis.framework.common.entity.BaseEntity;
import org.quartz.Job;
import org.quartz.JobBuilder;
import org.quartz.JobDataMap;
import org.quartz.JobDetail;
import org.quartz.JobKey;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Document(collection = "quartz_jobs")
@CompoundIndex(name = "job_name_group_idx", def = "{'jobName': 1, 'jobGroup': 1}", unique = true)
public class QuartzJobDO extends BaseEntity {

    private String jobName;
    private String jobGroup;
    private String description;
    private String jobClass;
    private JobDataMap jobDataMap;
    private boolean durability;
    private boolean shouldRecover;
    private String jobClassName;

    public QuartzJobDO(JobDetail jobDetail) {
        JobKey jobKey = jobDetail.getKey();
        this.jobName = jobKey.getName();
        this.jobGroup = jobKey.getGroup();
        this.description = jobDetail.getDescription();
        this.jobClass = jobDetail.getJobClass().getName();
        this.jobDataMap = jobDetail.getJobDataMap();
        this.durability = jobDetail.isDurable();
        this.shouldRecover = jobDetail.requestsRecovery();
    }

    public JobKey getJobKey() {
        return new JobKey(jobName, jobGroup);
    }

    public JobDetail getJobDetail() {
        try {
            @SuppressWarnings("unchecked")
            Class<? extends Job> clazz = (Class<? extends Job>) Class.forName(jobClass);
            return JobBuilder.newJob(clazz)
                    .withIdentity(getJobKey())
                    .withDescription(description)
                    .usingJobData(jobDataMap)
                    .storeDurably(durability)
                    .requestRecovery(shouldRecover)
                    .build();
        } catch (ClassNotFoundException e) {
            // This should not happen if the job class was validated upon storage
            throw new IllegalStateException("Cannot load job class " + jobClass, e);
        }
    }

} 