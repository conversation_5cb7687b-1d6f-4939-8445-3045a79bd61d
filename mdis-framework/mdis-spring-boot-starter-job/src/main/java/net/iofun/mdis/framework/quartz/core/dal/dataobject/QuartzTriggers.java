package net.iofun.mdis.framework.quartz.core.dal.dataobject;

import lombok.Data;
import net.iofun.mdis.framework.common.entity.BaseEntity;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 */
@Data
@Document("quartz_triggers")
// @CompoundIndex(name = "idx_trigger_name_group", def = "{'triggerName': 1, 'triggerGroup': 1}", unique = true)
// @CompoundIndex(name = "idx_job_name_group", def = "{'jobName': 1, 'jobGroup': 1}")
public class QuartzTriggers extends BaseEntity {

    private String schedName;       // 调度器名称
    private String triggerName;     // 触发器名称
    private String triggerGroup;    // 触发器组
    private String jobName;         // 关联的作业名称
    private String jobGroup;        // 关联的作业组
    private String description;     // 描述

    // @Indexed
    private Long nextFireTime;      // 下次触发时间
    private Long prevFireTime;      // 上次触发时间
    private Integer priority;       // 优先级

//    @Indexed
    private String triggerState;    // 触发器状态: WAITING, PAUSED, ACQUIRED, BLOCKED
    private String triggerType;     // 触发器类型: CRON, SIMPLE
    private Long startTime;         // 开始时间
    private Long endTime;           // 结束时间

    // CRON触发器特有属性
    private String cronExpression;  // CRON表达式
    private String timeZoneId;      // 时区ID

    private byte[] jobData;         // 触发器数据
}