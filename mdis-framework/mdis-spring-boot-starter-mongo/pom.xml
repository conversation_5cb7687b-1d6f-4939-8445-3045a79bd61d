<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>net.iofun</groupId>
        <artifactId>mdis-framework</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>mdis-spring-boot-starter-mongo</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>MongoDB 封装拓展</description>

    <dependencies>
        <dependency>
            <groupId>net.iofun</groupId>
            <artifactId>mdis-common</artifactId>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>net.iofun</groupId>
            <artifactId>mdis-spring-boot-starter-biz-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>net.iofun</groupId>
            <artifactId>mdis-spring-boot-starter-datapermission</artifactId>
        </dependency>

        <!-- Web 相关 -->
<!--        <dependency>-->
<!--            <groupId>net.iofun</groupId>-->
<!--            <artifactId>mdis-spring-boot-starter-web</artifactId>-->
<!--            <scope>provided</scope> &lt;!&ndash; 设置为 provided，只有 OncePerRequestFilter 使用到 &ndash;&gt;-->
<!--        </dependency>-->

        <dependency>
            <groupId>net.iofun</groupId>
            <artifactId>mdis-spring-boot-starter-security</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>mongodb-driver-sync</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>mongodb-driver-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId> <!-- 实现对 Caches 的自动化配置 -->
        </dependency>

        <!-- 方便等会写单元测试 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- 工具相关 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>net.iofun</groupId>
            <artifactId>mdis-spring-boot-starter-excel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fhs-opensource</groupId> <!-- VO 数据翻译 -->
            <artifactId>easy-trans-spring-boot-starter</artifactId>
        </dependency>

    </dependencies>

</project>