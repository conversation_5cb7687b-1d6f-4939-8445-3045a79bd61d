package net.iofun.mdis.framework.mongo.core.util;

import com.mongodb.ConnectionString;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import lombok.extern.slf4j.Slf4j;
import net.iofun.mdis.framework.common.pojo.SortingField;
import org.bson.BsonType;
import org.bson.Document;
import org.bson.types.Binary;
import org.bson.types.Decimal128;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Query;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 */
@Slf4j
public class MongoUtils {
    private MongoUtils() {
        // 私有构造函数防止实例化
    }

    /**
     * set query sorting according to request
     *
     * @param query
     * @param sorts
     */
    public static void applySort(Query query, List<String> sorts) {

        if (query == null || sorts == null)
            return;

        sorts.forEach(field -> {
            if (!StringUtils.isEmpty(field)) {
                String[] str = field.split("\\s+");
                if (str.length == 2)
                    Sort.Direction.fromOptionalString(str[1]).ifPresent(direction -> {
                        query.with(Sort.by(direction, str[0]));
                    });
            }
        });

    }

    // 把 SortingField 类型更新或添加到 Sort.Order list
    public static void upsertOrders(List<Sort.Order> orderList, SortingField sortingField) {

        Assert.notNull(sortingField, "sortingField must not be null");
        Assert.notNull(orderList, "orderList must not be null");

        // 检查现有的 orderList 是否包含 sortingField 的字段, 如果包含就更新,否则就添加
        boolean[] found = {false};
        orderList.forEach(order -> {
            if (order.getProperty().equals(sortingField.getField())) {
                found[0] = true;
                if ((order.isAscending() && SortingField.ORDER_DESC.equals(sortingField.getOrder()))
                        || (order.isAscending() && SortingField.ORDER_DESC.equals(sortingField.getOrder()))) {
                    order.reverse();
                }
            }
        });
        if (!found[0]) {
            orderList.add(new Sort.Order(SortingField.ORDER_ASC.equals(sortingField.getOrder()) ? Sort.Direction.ASC : Sort.Direction.DESC, sortingField.getField()));
        }
    }

    // 更新或添加新的 Sort.Order
    public static void upsertOrders(List<Sort.Order> orderList, Sort.Order newOrder) {

        Assert.notNull(newOrder, "sortOrder must not be null");
        Assert.notNull(orderList, "orderList must not be null");

        // 检查现有的 orderList 是否包含 order 的字段, 如果包含就更新,否则就添加
        boolean[] found = {false};
        orderList.forEach(order -> {
            if (order.getProperty().equals(newOrder.getProperty())) {
                found[0] = true;
                if ((order.isAscending() && newOrder.isDescending()) || (order.isDescending() && newOrder.isAscending())) {
                    order.reverse();
                }
            }
        });
        if (!found[0]) {
            orderList.add(newOrder);
        }
    }

    /**
     * 创建 MongoDB 客户端连接
     *
     * @param mongoUri MongoDB 连接 URI
     * @return MongoDB 客户端实例
     */
    public static MongoClient createMongoClient(String mongoUri) {
        return MongoClients.create(MongoClientSettings.builder()
                .applyConnectionString(new ConnectionString(mongoUri))
                .build());
    }

    /**
     * 从 MongoDB URI 中提取数据库名称
     *
     * @param mongoUri MongoDB 连接 URI
     * @return 数据库名称
     * @throws IllegalArgumentException 如果数据库名称为空
     */
    public static String extractDatabaseName(String mongoUri) {
        String dbName = new ConnectionString(mongoUri).getDatabase();
        Assert.notNull(dbName, "MongoDB database name cannot be empty!");
        return dbName;
    }

    /**
     * 根据 Java 对象获取对应的 BSON 类型
     *
     * @param value Java 对象值
     * @return 对应的 BsonType，如果无法识别则返回 null
     */
    public static BsonType getBsonType(Object value) {
        if (value == null) {
            return BsonType.NULL;
        } else if (value instanceof ObjectId) {
            return BsonType.OBJECT_ID;
        } else if (value instanceof String) {
            return BsonType.STRING;
        } else if (value instanceof Integer) {
            return BsonType.INT32;
        } else if (value instanceof Long) {
            return BsonType.INT64;
        } else if (value instanceof Double) {
            return BsonType.DOUBLE;
        } else if (value instanceof Boolean) {
            return BsonType.BOOLEAN;
        } else if ((value instanceof LocalDateTime) || (value instanceof Date)) {
            return BsonType.DATE_TIME;
        } else if (value instanceof Document) {
            return BsonType.DOCUMENT;
        } else if (value instanceof List) {
            return BsonType.ARRAY;
        } else if ((value instanceof byte[]) || (value instanceof Binary)) {
            return BsonType.BINARY;
        } else if ((value instanceof BigDecimal) || (value instanceof Decimal128)) {
            return BsonType.DECIMAL128;
        }
        return BsonType.UNDEFINED; // 使用 UNDEFINED 替代 null 作为未知类型的标识
    }

    /**
     * 获取字段的 BSON 类型名称
     *
     * @param value Java 对象值
     * @return BSON 类型名称
     */
    public static String getBsonTypeName(Object value) {
        BsonType bsonType = getBsonType(value);
        return bsonType != null ? bsonType.toString() : "undefined";
    }

    /**
     * 获取对象的 Java 类型名称
     *
     * @param value Java 对象值
     * @return Java 类型名称
     */
    public static String getJavaTypeName(Object value) {
        if (value == null) {
            return "null";
        }
        if (value.getClass().getSimpleName().equals("Decimal128")) {
            return "BigDecimal";
        }
        if (value.getClass().getSimpleName().equals("Date")) {
            return "LocalDateTime";
        }
        if (value.getClass().getSimpleName().equals("Binary")) {
            return "byte[]";
        }
        if (value.getClass().getSimpleName().equals("ArrayList")) {
            return "List<?>";
        }
        if (value.getClass().getSimpleName().equals("Document")) {
            return "Object";
        }
        return value.getClass().getSimpleName();
    }

}
