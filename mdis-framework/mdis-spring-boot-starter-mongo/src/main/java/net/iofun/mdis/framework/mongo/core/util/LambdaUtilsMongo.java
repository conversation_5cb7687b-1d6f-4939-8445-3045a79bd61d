package net.iofun.mdis.framework.mongo.core.util;

import java.beans.Introspector;
import java.io.Serializable;
import java.lang.invoke.SerializedLambda;
import java.lang.reflect.Method;

/**
 * Lambda 表达式解析工具，用于从方法引用中提取字段名称。
 */
public final class LambdaUtilsMongo {

    private LambdaUtilsMongo() {
        // 工具类禁止实例化
    }

    /**
     * 解析字段名称
     *
     * @param lambda Lambda 表达式
     * @param <T>    所属类
     * @return 字段名称
     */
    public static <T> String extractFieldName(Serializable lambda) {
        try {
            // 获取 SerializedLambda 实例
            SerializedLambda serializedLambda = getSerializedLambda(lambda);
            // 获取方法名
            String methodName = serializedLambda.getImplMethodName();
            // 如果方法是 "getXxx"，转换为字段名 "xxx"
            if (methodName.startsWith("get")) {
                return Introspector.decapitalize(methodName.substring(3));
            }
            // 如果方法是 "isXxx"，转换为字段名 "xxx"（布尔字段）
            if (methodName.startsWith("is")) {
                return Introspector.decapitalize(methodName.substring(2));
            }
            throw new IllegalArgumentException("无法从方法名称中提取字段：" + methodName);
        } catch (Exception e) {
            throw new RuntimeException("Lambda 表达式解析失败", e);
        }
    }

    /**
     * 获取 SerializedLambda
     *
     * @param lambda Lambda 表达式
     * @return SerializedLambda
     */
    private static SerializedLambda getSerializedLambda(Serializable lambda) {
        try {
            // 调用 Lambda 的 writeReplace 方法
            Method writeReplace = lambda.getClass().getDeclaredMethod("writeReplace");
            writeReplace.setAccessible(true);
            return (SerializedLambda) writeReplace.invoke(lambda);
        } catch (Exception e) {
            throw new RuntimeException("获取 SerializedLambda 失败", e);
        }
    }
}
