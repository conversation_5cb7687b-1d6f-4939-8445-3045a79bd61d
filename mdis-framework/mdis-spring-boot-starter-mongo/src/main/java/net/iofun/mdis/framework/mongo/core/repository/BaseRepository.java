package net.iofun.mdis.framework.mongo.core.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.mongodb.client.ListIndexesIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.IndexOptions;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.iofun.mdis.framework.common.pojo.PageParam;
import net.iofun.mdis.framework.common.pojo.PageResult;
import net.iofun.mdis.framework.common.pojo.SortablePageParam;
import net.iofun.mdis.framework.common.pojo.SortingField;
import net.iofun.mdis.framework.common.annotation.SetOnInsert;
import net.iofun.mdis.framework.common.entity.BaseEntity;
import net.iofun.mdis.framework.common.entity.TenantBaseEntity;
import net.iofun.mdis.framework.mongo.core.util.MongoUtils;
import net.iofun.mdis.framework.mongo.core.util.ReflectionUtils;
import net.iofun.mdis.framework.security.core.LoginUser;
import net.iofun.mdis.framework.tenant.core.context.TenantContextHolder;
import org.bson.BsonDocument;
import org.bson.BsonValue;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.*;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.mongodb.repository.query.MongoEntityInformation;
import org.springframework.data.mongodb.repository.support.MappingMongoEntityInformation;
import org.springframework.data.mongodb.repository.support.MongoRepositoryFactory;
import org.springframework.data.util.Streamable;
import org.springframework.util.Assert;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static org.springframework.data.mongodb.core.query.Criteria.where;

/**
 * <AUTHOR>
 */
@Slf4j
@Getter
public abstract class BaseRepository<Entity extends BaseEntity, ID> {

    private static MongoRepositoryFactory repositoryFactory;
    protected final MongoTemplate mongoOperations;
    protected final MappingMongoEntityInformation<Entity, ID> entityInformation;

    @Value("${spring.data.mongodb.cursorBatchSize}")
    protected int cursorBatchSize = 1000;

    private final Class<Entity> entityClass;

    public BaseRepository(Class<Entity> entityClass, MongoTemplate mongoOperations) {
        this.entityInformation = (MappingMongoEntityInformation<Entity, ID>) getEntityInformation(entityClass, mongoOperations);
        this.mongoOperations = mongoOperations;
        this.entityClass = entityClass;
        init();
    }

    private static <Entity, ID> MongoEntityInformation<Entity, ID> getEntityInformation(Class<Entity> tClass, MongoTemplate mongoOperations) {

        if (repositoryFactory == null)
            repositoryFactory = new MongoRepositoryFactory(mongoOperations);

        return repositoryFactory.getEntityInformation(tClass);
    }

    protected void init() {}

    protected void createIndex(BsonDocument bson, IndexOptions indexOptions) {
        MongoCollection<Document> collection = mongoOperations.getCollection(entityInformation.getCollectionName());

        ListIndexesIterable<Document> indexes = collection.listIndexes();

        boolean isExist = false;
        end:
        for (Document document : indexes) {
            Map<String, Object> indexKey = (Map<String, Object>) document.get("key");
            List<Map.Entry<String, BsonValue>> entries = new ArrayList<>(bson.entrySet());
            if (indexKey.size() != entries.size()) continue;
            for (int i = 0; i < entries.size(); i++) {
                Map.Entry<String, BsonValue> entry = entries.get(i);
                if (!indexKey.containsKey(entry.getKey()) || entry.getValue().equals(indexKey.get(entry.getKey()))) {
                    break;
                }
                if (i == entries.size() - 1) {
                    isExist = true;
                    break end;
                }
            }
        }

        if (!isExist) {
            collection.createIndex(bson, indexOptions);
        }
    }

    public MongoTemplate getMongoOperations() {
        return mongoOperations;
    }

    public String getCollectionName() {
        return entityInformation.getCollectionName();
    }


    // -------- select --------
//    public PageResult<Entity> selectPage(PageParam pageParam, LambdaQueryWrapperMongo<Entity> queryWrapper, LoginUser loginUser) {
//    public PageResult<Entity> selectPage(PageParam pageParam, Query query, LoginUser loginUser) {
    public PageResult<Entity> selectPage(PageParam pageParam, Query query) {
        return selectPage(pageParam, null,query);
    }

//    public PageResult<Entity> selectPage(SortablePageParam pageParam, LambdaQueryWrapperMongo<Entity> queryWrapper, LoginUser loginUser) {
//    public PageResult<Entity> selectPage(SortablePageParam pageParam, Query query, LoginUser loginUser) {
    public PageResult<Entity> selectPage(SortablePageParam pageParam, Query query) {
        return selectPage(pageParam, pageParam.getSortingFields(), query);
    }

//    public PageResult<Entity> selectPage(PageParam pageParam, Collection<SortingField> sortingFields, LambdaQueryWrapperMongo<Entity> queryWrapper, LoginUser loginUser) {
//    public PageResult<Entity> selectPage(PageParam pageParam, Collection<SortingField> sortingFields, Query query, LoginUser loginUser) {
    public PageResult<Entity> selectPage(PageParam pageParam, Collection<SortingField> sortingFields, Query query) {

        // 创建排序条件
        List<Sort.Order> orderList = new ArrayList<>();

        // 如果 sortingFields 不空, 则把 sortingFields中的排序加入 orderList
        if (CollectionUtil.isNotEmpty(sortingFields)) {
            sortingFields.forEach(field -> MongoUtils.upsertOrders(orderList, field));
        }

        Sort sort = Sort.by(orderList);

        // queryWrapper中自带的 sort 优先级更高, 在queryWrapper中没有 sort 的情况下, 才用sortingFields中的 sort
        if (query.isSorted()) {
            Document sortObject = query.getSortObject();
            log.info("query's sortObject:{}", sortObject);
        } else {
            query.with(sort);
        }

        // 没有设定 Page size 的情况下
        if (PageParam.PAGE_SIZE_NONE.equals(pageParam.getPageSize())) {
//            List<Entity> list_rs = selectList(query, loginUser);
            List<Entity> list_rs = selectList(query);
            return new PageResult<>(list_rs, (long) list_rs.size());
        }

        // 设定 Page size 的情况下, 创建分页条件, pageNo是从 0 开始的
        Pageable pageable = PageRequest.of(pageParam.getPageNo()-1, pageParam.getPageSize(), sort);
        // 执行分页操作
//        Page<Entity> page_rs = findAll(pageable,query, loginUser);
        Page<Entity> page_rs = findAll(pageable,query);

        return new PageResult<>(page_rs.getContent(), page_rs.getTotalElements());
    }

//    public List<Entity> selectList(Query query, LoginUser loginUser) {
    public List<Entity> selectList(Query query) {
        Assert.notNull(query, "query must not be null!");
//        return findAll(query, loginUser);
        return findAll(query);
    }

//    public Optional<Entity> selectById(Query query, LoginUser loginUser) {
    public Optional<Entity> selectById(Query query) {
        Assert.notNull(query, "query must not be null!");

//        if (loginUser == null) { // 还未登录, loginUSer 为空
//            return findOne(query);
//        } else { // 已经登录, 可获取 loginUser.getTenantId()
//            return findOne(query, loginUser);
//        }
        return findOne(query);
    }

//    public Page<Entity> findAll(Pageable pageable, LambdaQueryWrapperMongo<Entity> queryWrapper, LoginUser loginUser) {
//    public Page<Entity> findAll(Pageable pageable, Query query, LoginUser loginUser) {
    public Page<Entity> findAll(Pageable pageable, Query query) {
        Assert.notNull(pageable, "Pageable must not be null");

        // queryWrapper 在没有带上 pageable 前, Count 出总条数
//        long count = this.count(query, loginUser);
        long count = this.count(query);

        // 这里的 list_rs 的size 只是一个 page 的默认 size, 不能用作查询的总条数
//        List<Entity> list_rs = this.findAll(query.with(pageable), loginUser);
        List<Entity> list_rs = this.findAll(query.with(pageable));

        return new PageImpl<>(list_rs, pageable, count);
    }

    // -------- select base --------
//    public List<Entity> findAll(Query query, LoginUser loginUser) {
    public List<Entity> findAll(Query query) {
        Assert.notNull(query, "Query must not be null!");

        // 默认只查没有被软删除的,和 tenantID 匹配的
        addDefaultDeletedCriteriaIfAbsent(query);
        return mongoOperations.find(query, entityClass);
    }

//    public Optional<Entity> findOne(Query query, LoginUser loginUser) {
//        Assert.notNull(query, "Query must not be null!");
//        return findOne(query);
//    }

    public Optional<Entity> findOne(Query query) {
        Assert.notNull(query, "Query must not be null!");

        // 只查没有被软删除的;
        addDefaultDeletedCriteriaIfAbsent(query);

        return Optional.ofNullable(
                mongoOperations.findOne(query, entityInformation.getJavaType(), entityInformation.getCollectionName()));
    }

//    public boolean existsByQuery(Query query, LoginUser loginUser) {
//        return existsByQuery(query);
//    }

    public boolean existsByQuery(Query query) {
        Assert.notNull(query, "The query must not be null!");

        // 默认只查没有被软删除的
        addDefaultDeletedCriteriaIfAbsent(query);
        return mongoOperations.exists(query, entityInformation.getJavaType(),
                entityInformation.getCollectionName());
    }

    // -------- insert --------
    public Entity insert(Entity entity, LoginUser loginUser) {
        Assert.notNull(entity, "Entity must not be null!");
        if (loginUser == null) {
            log.info("[insert] LoginUser is null, using system, clazz: "+ entityClass.getName());
            // 主要在ApiErrorLogServiceImpl#createApiErrorLog时,apiErrorLogRepository.insert(apiErrorLog, getLoginUser())中, 传入的 loginUser 为 null, 所以需要传入 system 用户
            return insert(entity, "system");
        }
        return insert(entity, loginUser.getId());
    }

    public Entity insert(Entity entity, String loginUserId) { // 未获取LoginUser, 创建租户同时创建角色和用户(tenantId 是新建的,而不是 LoginUser 携带的)的情况下, 传入 userId, tenantId
        Assert.notNull(entity, "Entity must not be null!");

        // 新增, 填入默认值
        beforeCreateEntity(entity, loginUserId); // 如果有 tenantId 字段 TenantContextHandler 会 set

        return mongoOperations.insert(entity, entityInformation.getCollectionName());
    }

    public List<Entity> saveAll(Iterable<Entity> entities, LoginUser loginUser) {
        Assert.notNull(entities, "The given Iterable of entities not be null!");
        if (loginUser == null) {
            log.info("[saveAll] LoginUser is null, using system, clazz: "+ entityClass.getName());
            return saveAll(entities, "system");
        }
        return saveAll(entities, loginUser.getId());
    }

    public List<Entity> saveAll(Iterable<Entity> entities, String loginUserId) {
        Assert.notNull(entities, "The given Iterable of entities not be null!");

        Streamable<Entity> source = Streamable.of(entities);

        boolean allNew = source.stream().allMatch(entityInformation::isNew);

        if (allNew) {
            source.forEach(entity -> beforeCreateEntity(entity, loginUserId));
            List<Entity> result = source.stream().collect(Collectors.toList());
            return new ArrayList<>(mongoOperations.insert(result, entityInformation.getCollectionName()));
        }

        return source.stream().map(entity -> save(entity, loginUserId)).collect(Collectors.toList());
    }

    public Entity save(Entity entity, String loginUserId) {
        Assert.notNull(entity, "Entity must not be null!");

        if (entityInformation.isNew(entity)) {
            beforeCreateEntity(entity, loginUserId);
            return mongoOperations.insert(entity, entityInformation.getCollectionName());
        }
        beforeUpdateEntity(entity, loginUserId);

        Query query = getIdQuery(entity.getId());

        beforeUpdateEntity(entity, loginUserId);

        Update update = buildUpdateSet(entity); // 如果有 tenantId,会有 set

        UpdateResult result = mongoOperations.updateFirst(query, update, entityInformation.getJavaType());

        if (result.getMatchedCount() == 1) {
            return findOne(query).orElse(entity);
        }
        return entity;
    }

    // -------- update ------
    public UpdateResult updateMany(Query query, Update update, LoginUser loginUser) { // 在 Query 中对于有 tenantId 字段的会自动在 LambdaQueryWrapperMongo 中添加查询规则

        addDefaultDeletedCriteriaIfAbsent(query);

        // 更新设置, updateTime, updater 默认值
        if (loginUser == null) {
            log.info("[updateMany] LoginUser is null, using system, clazz: "+ entityClass.getName());
            addDefaultUpdateTimeAndUpdaterIfAbsent(update, "system");
        } else {
            addDefaultUpdateTimeAndUpdaterIfAbsent(update, loginUser.getId());
        }

        return mongoOperations.updateMulti(query, update, entityClass);
    }

    public UpdateResult updateById(Entity entity, LoginUser loginUser) {
        Assert.notNull(entity, "Entity must not be null!");
        if (loginUser == null) {
            log.info("[updateById] LoginUser is null, using system, clazz: "+ entityClass.getName());
            return updateById(entity, "system");
        }
        return updateById(entity, loginUser.getId());
    }

    public UpdateResult updateById(Entity entity, String updateUserId) {
        Assert.notNull(entity, "Entity must not be null!");
        Assert.notNull(updateUserId, "UpdateUser id must not be null!");

        // 查询条件,默认是根据 id
        Query query = getIdQuery(entity);
        addDefaultDeletedCriteriaIfAbsent(query);

        // 单个实体的更新设置, updateTime, updater 默认值
        beforeUpdateEntity(entity, updateUserId);

        Update update = buildUpdateSet(entity);

        return updateFirst(query, update);
    }

    public UpdateResult updateFirst(Query query, Update update) {
        Assert.notNull(query, "Query must not be null!");
        Assert.notNull(update, "Update must not be null!");

        return mongoOperations.updateFirst(query, update, entityClass);
    }

    public UpdateResult upsert(Query query, Entity entity, LoginUser loginUser) {
        Assert.notNull(query, "Query must not be null!");
        
        addDefaultDeletedCriteriaIfAbsent(query);

        if (loginUser == null) {
            log.info("[upsert] LoginUser is null, using system, clazz: "+ entityClass.getName());
            beforeUpdateEntity(entity, "system");
        } else {
            beforeUpdateEntity(entity, loginUser.getId());
        }

        Update update = buildUpdateSet(entity);

        return mongoOperations.upsert(query, update, entityClass);

    }

    // -------- 软删除, 其实是更新 deleted = true --------
    public UpdateResult softDeleteByQuery(Query query, LoginUser loginUser) {
        Assert.notNull(query, "Query must not be null!");

        if (loginUser == null) {
            log.info("[softDeleteByQuery] LoginUser is null, using system, clazz: "+ entityClass.getName());
            return softDeleteByQuery(query, "system");
        }
        return softDeleteByQuery(query, loginUser.getId());
    }

    public UpdateResult softDeleteByQuery(Query query, String loginUserId) {
        Assert.notNull(query, "Query must not be null!");

        // 更新设置, updateTime, updater 默认值, deleted = true
        Update update = buildSoftDeleteUpdate(loginUserId);

        return mongoOperations.updateMulti(query, update, entityClass);
    }

    // -------- 真删除 --------
    public DeleteResult deleteByQuery(Query query) {
        Assert.notNull(query, "Query must not be null!");
        return mongoOperations.remove(query, entityInformation.getJavaType(), entityInformation.getCollectionName());
    }

    // --------
    // 对一个实体构建更新的 Update, 顺序上在 beforeUpdateEntity, beforeCreateEntity 后面执行
    public Update buildUpdateSet(Entity entity) {
        Update update = new Update();
        Field[] fields = ReflectionUtils.getAllDeclaredFields(entityClass);

        for (int i = 0; i < fields.length; i++) {
            Field field = fields[i];
            if (Modifier.isStatic(field.getModifiers()) || "$jacocoData".equals(field.getName())) {
                //  skip 字段是静态 或者 jaCOCO 代码覆盖工具
                continue;
            }
            Object value = ReflectionUtils.getField(field, entity);
            if (value != null) { // 如果 Entity 的 field 有 value,
                String fieldName = field.getName();
                SetOnInsert setOnInsert = field.getAnnotation(SetOnInsert.class);
                if (setOnInsert != null) { // 有 SetOnInsert 注解的, 只有在新增时 value 会写入, 更新时不会
                    update.setOnInsert(fieldName, value);
                } else { // 没有 SetOnInsert 注解的, 新增和更新时 value 都会写入
                    update.set(fieldName, value);
                }
            }
        }
        return update;
    }

    // 软删除, 更新前把默认的更新时间,更新者,deleted 标志, 设置到 update
    public Update buildSoftDeleteUpdate(String loginUserId) {
        Assert.notNull(loginUserId, "loginUser id must not be null!");
        Update update = new Update();
        update.set("deleted", true);
        update.set("update_time", LocalDateTime.now());
        update.set("updater", loginUserId);
        return update;
    }

    // 新增前把默认的新增时间,新增者,租户 id, 未删除标志,更新时间,更新者, 加入到 Entity
    public void beforeCreateEntity(Entity entity, LoginUser loginUser) {
        Assert.notNull(loginUser, "loginUser must not be null!");
        beforeCreateEntity(entity, loginUser.getId());
    }

     public void beforeCreateEntity(Entity entity, String loginUserId) { // 还未获取 LoginUser 情况下, 传入 userId, tenantId
        Assert.notNull(loginUserId, "loginUserId must not be null!");
        Assert.notNull(entity, "entity must not be null!");
        // upsert 时是 setOnInsert 字段
        entity.setCreateTime(LocalDateTime.now());
        entity.setCreator(loginUserId);

        try {
            // 尝试获取租户ID，但不强制要求
            String ctxTenantId = TenantContextHolder.getTenantId();
            
            // 只有在租户ID不为空时才设置
            if (ctxTenantId != null && TenantBaseEntity.class.isAssignableFrom(entity.getClass())) {
                // tenantId 字段在 BaseEntity 中没有, 但是在它的子类中有, 就添加
                ((TenantBaseEntity)entity).setTenantId(ctxTenantId);
            }
        } catch (Exception e) {
            // 忽略租户ID获取失败的情况
            log.warn("[beforeCreateEntity] 获取租户ID失败: {}", e.getMessage());
        }
        
        // 非 setOnInsert 字段
        entity.setDeleted(false);
        beforeUpdateEntity(entity, loginUserId);
    }

    // 更新前把默认的更新时间和更新者, 加入到 Entity
    public void beforeUpdateEntity(Entity entity, String loginUserId) {
        Assert.notNull(loginUserId, "loginUser id not be null!");
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdater(loginUserId);
    }

    // 把默认的更新时间和更新者添加到 Update 对象
    public void addDefaultUpdateTimeAndUpdaterIfAbsent(Update update, String loginUserId) {
        Assert.notNull(loginUserId, "loginUser id must not be null!");
        // 先查看 Update 是否已经有了 'update_time' 和 'updater' 的设置
        if (!update.getUpdateObject().containsKey("update_time")) {
            update.set("update_time", LocalDateTime.now());
        }
        if (!update.getUpdateObject().containsKey("updater")) {
            update.set("updater", loginUserId);
        }
    }

    // -------- count --------
//    public long count(Query query, LoginUser loginUser) {
    public long count(Query query) {
        Assert.notNull(query, "Query must not be null!");

        addDefaultDeletedCriteriaIfAbsent(query);

        return mongoOperations.count(query, entityInformation.getJavaType(), entityInformation.getCollectionName());
    }

    public long estimatedCount() {
        return mongoOperations.estimatedCount(entityInformation.getCollectionName());
    }

    // -------- Query Criteria ----
    public Query getIdQuery(Entity entity) {
        return new Query(setIdCriteria(entity));
    }

    public Query getIdQuery(Object id) {
        return new Query(setIdCriteria(id));
    }

    public void addDefaultDeletedCriteriaIfAbsent(Query query) {
        if (! query.getQueryObject().containsKey("deleted")) {
            query.addCriteria(setDefaultDeletedCriteria());
        }
    }

    public Criteria setIdCriteria(Entity entity) {
        return where(entityInformation.getIdAttribute()).is(entity.getId());
    }

    public Criteria setIdCriteria(Object id) {
        return where(entityInformation.getIdAttribute()).is(id);
    }

    public Criteria setDefaultDeletedCriteria() {
        return where("deleted").is(false);
    }

}
