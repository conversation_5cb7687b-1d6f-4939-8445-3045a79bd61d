package net.iofun.mdis.framework.mongo.core.util;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class ReflectionUtils {
    public ReflectionUtils() {}

    public static <T> Field[] getAllDeclaredFields(Class<T> clazz) {
        List<Field> allFields = collectFieldsFromClassHierarchy(clazz); // Extracted utility function

        // Convert collected fields to an array
        return allFields.toArray(new Field[0]);
    }

    /**
     * Utility method to collect all declared fields from a class and its superclasses.
     *
     * @param clazz the class to traverse
     * @return a list of fields from the class hierarchy
     */
    private static List<Field> collectFieldsFromClassHierarchy(Class<?> clazz) {
        List<Field> fieldList = new ArrayList<>();
        while (clazz != null) {
            fieldList.addAll(Arrays.asList(clazz.getDeclaredFields()));
            clazz = clazz.getSuperclass(); // Traverse to the superclass
        }
        return fieldList;
    }

    public static <T> Field[] getAllDeclaredFieldsIgnoreSuper(Class<T> clazz) {
        // 避免未定义行为，直接获取当前类的声明字段
        Field[] declaredFields = clazz.getDeclaredFields();

        // 如果没有字段，直接返回一个空数组
        if (declaredFields.length == 0) {
            return new Field[0];
        }

        // 直接返回字段数组，避免无必要的额外拷贝
        return Arrays.copyOf(declaredFields, declaredFields.length);
    }

    public static Map<String, Class<?>> getAllFieldType(Class<?> clazz) {
        // 使用 Stream API 提升代码简洁性
        return Arrays.stream(getAllDeclaredFields(clazz))
                .collect(Collectors.toMap(Field::getName, Field::getType));
    }

    public static Object getField(Field field, Object target) {
        try {
            field.setAccessible(true);
            return field.get(target);
        } catch (IllegalAccessException e) {
            e.printStackTrace();
            return null;
        }
    }
}
