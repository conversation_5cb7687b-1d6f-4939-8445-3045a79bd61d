package net.iofun.mdis.framework.mongo.translate.config;

import com.fhs.trans.service.impl.SimpleTransService;
import com.fhs.trans.service.impl.TransService;
import net.iofun.mdis.framework.mongo.translate.core.TranslateUtils;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

@AutoConfiguration
public class MdisTranslateAutoConfiguration {

    /**
     * 提供一个SimpleTransService的实现，用于MongoDB环境
     */
    @Bean
    @ConditionalOnMissingBean(SimpleTransService.class)
    public SimpleTransService simpleTransService() {
        // 创建一个简单的实现，不依赖于具体的数据库
        return new SimpleTransService();
    }

    @Bean
    @Primary
    @SuppressWarnings({"InstantiationOfUtilityClass", "SpringJavaInjectionPointsAutowiringInspection"})
    public TranslateUtils translateUtils(TransService transService) {
        TranslateUtils.init(transService);
        return new TranslateUtils();
    }

}
