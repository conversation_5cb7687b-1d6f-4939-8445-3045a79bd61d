package net.iofun.mdis.framework.mongo.core.query;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import net.iofun.mdis.framework.common.util.collection.ArrayUtils;
import net.iofun.mdis.framework.common.entity.TenantBaseEntity;
import net.iofun.mdis.framework.mongo.core.util.LambdaUtilsMongo;
import net.iofun.mdis.framework.mongo.core.util.SFunctionMongo;
import net.iofun.mdis.framework.datapermission.core.aop.DataPermissionMongoContext;
import net.iofun.mdis.framework.tenant.core.util.TenantUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import static net.iofun.mdis.framework.mongo.core.util.LambdaUtilsMongo.extractFieldName;

/**
 * <AUTHOR>
 */
@Slf4j
public class LambdaQueryWrapperMongo<T> extends Query implements Serializable {

    private final Class<T> entityClass;

    // 增强的构造方法
    public LambdaQueryWrapperMongo(Class<T> entityClass) {
        this.entityClass = entityClass;
        boolean isTenantIgnored = TenantUtils.ignoreTable(entityClass.getAnnotation(Document.class).value());

        // 继承了 TenantBaseEntity, 且 entityClass 的 MongoDB 的 表名非忽略 tenantId 的, 添加对 tenant_id 的查询
        if (TenantBaseEntity.class.isAssignableFrom(entityClass) && !isTenantIgnored) {
            this.addCriteria(TenantUtils.getTenantIdCriteria());
        }
        applyDataPermissionConditions();
    }

    protected Class<T> getEntityClass() {
        return entityClass;
    }

    private void applyDataPermissionConditions() {
        List<Criteria> criteriaList =
            DataPermissionMongoContext.getCriterias(getEntityClass());

        if (criteriaList == null || criteriaList.isEmpty()) {
            return;
        }

        // 把 criteriaList 中的查询条件, 加入 this 这个对象实例
        criteriaList.forEach(this::addCriteria);

    }

    /**
     * 根据字段名和值创建 LIKE 条件（仅当值存在时）
     *
     * @param column 字段方法引用（如 LaborWagesDO::getTeam）
     * @param val  匹配的值
     * @return 当前 Wrapper
     */
    public LambdaQueryWrapperMongo<T> likeIfPresent(SFunctionMongo<T, ?> column, String val) {
        if (StringUtils.hasText(val)) {
            // 使用 LambdaUtilsMongo 提取字段名
            String fieldName = extractFieldName(column);
            // 添加查询条件, Pattern.quote(val) 用于转义特殊字符
            this.addCriteria(Criteria.where(fieldName).regex(val));
        }
        return this;
    }

    public LambdaQueryWrapperMongo<T> betweenIfPresent(SFunctionMongo<T, ?> column, Object[] values) {
        Object val1 = ArrayUtils.get(values, 0);
        Object val2 = ArrayUtils.get(values, 1);
        return betweenIfPresent(column, val1, val2);
    }

    public LambdaQueryWrapperMongo<T> betweenIfPresent(SFunctionMongo<T, ?> column, Object val1, Object val2) {
        if (val1 != null && val2 != null) {
            this.addCriteria(Criteria.where(extractFieldName(column)).gte(val1).lte(val2));
            return this;
        }else if (val1 != null) {
            this.addCriteria(Criteria.where(extractFieldName(column)).gte(val1));
            return this;
        }else if (val2 != null) {
            this.addCriteria(Criteria.where(extractFieldName(column)).lte(val2));
            return this;
        }else {
            return this;
        }
    }

    public LambdaQueryWrapperMongo<T> in(SFunctionMongo<T, ?> column, Collection<?> values) {
        if (ObjectUtil.isAllNotEmpty(values) && !ArrayUtil.isEmpty(values)) {
            this.addCriteria(Criteria.where(extractFieldName(column)).in(values));
        } else { // 在 in 查询中, 如果查询值是空, 就不返回任何
            this.addCriteria(Criteria.where("_id").exists(false));
        }
        return this;
    }

    public LambdaQueryWrapperMongo<T> inIfPresent(SFunctionMongo<T, ?> column, Collection<?> values) {
        if (ObjectUtil.isAllNotEmpty(values) && !ArrayUtil.isEmpty(values)) {
            this.addCriteria(Criteria.where(extractFieldName(column)).in(values));
        }
        return this;
    }

    public LambdaQueryWrapperMongo<T> eqIfPresent(SFunctionMongo<T, ?> column, Object val) {
        if (ObjectUtil.isNotEmpty(val)) {
            this.addCriteria(Criteria.where(extractFieldName(column)).is(val));
        }
        return this;
    }

    public LambdaQueryWrapperMongo<T> eq(SFunctionMongo<T, ?> column, Object val) {
        Assert.notNull(val, "val must not be null");
        this.addCriteria(Criteria.where(extractFieldName(column)).is(val));
        return this;
    }

    public LambdaQueryWrapperMongo<T> gt(SFunctionMongo<T, ?> column, Object val) {
        Assert.notNull(val, "value must not be null");
        this.addCriteria(Criteria.where(extractFieldName(column)).gt(val));
        return this;
    }

    public LambdaQueryWrapperMongo<T> gteIfPresent(SFunctionMongo<T, ?> column, Object val) {
        if (ObjectUtil.isNotEmpty(val)) {
            this.addCriteria(Criteria.where(extractFieldName(column)).gte(val));
        }
        return this;
    }

    public LambdaQueryWrapperMongo<T> lteIfPresent(SFunctionMongo<T, ?> column, Object val) {
        if (ObjectUtil.isNotEmpty(val)) {
            this.addCriteria(Criteria.where(extractFieldName(column)).lte(val));
        }
        return this;
    }

     public LambdaQueryWrapperMongo<T> ne(SFunctionMongo<T, ?> column, Object val) {
         Assert.notNull(val, "value must not be null");
         this.addCriteria(Criteria.where(extractFieldName(column)).ne(val));
         return this;
     }
     public LambdaQueryWrapperMongo<T> isNotNull(SFunctionMongo<T, ?> column) {
         Assert.notNull(column, "column must not be null");
         this.addCriteria(Criteria.where(extractFieldName(column)).exists(true).ne(null));
         return this;
     }

    public LambdaQueryWrapperMongo<T> orderByDesc(SFunctionMongo<T, ?> column) {
        this.with(Sort.by(Sort.Direction.DESC, extractFieldName(column)));
        return this;
    }

    public LambdaQueryWrapperMongo<T> orderByAsc(SFunctionMongo<T, ?>... columns) {
        Assert.notNull(columns, "columns must not be null");
        // columns 中的每个 column 用 extractFieldName(column) 取出字段名字符串, 并且按照原来的顺序组成字符串数组
        // 使用流式处理将 columns 转换为字段名字符串数组
        String[] fieldNames = Arrays.stream(columns)
                .map(LambdaUtilsMongo::extractFieldName)
                .toArray(String[]::new);

        this.with(Sort.by(Sort.Direction.ASC, fieldNames));
        return this;
    }

    public LambdaQueryWrapperMongo<T> orderByAsc(SFunctionMongo<T, ?> column) {
        this.with(Sort.by(Sort.Direction.ASC, extractFieldName(column)));
        return this;
    }

    public LambdaQueryWrapperMongo<T> limitIfPresent(Integer limit) {
        if (limit != null) {
            this.limit(limit);
        }
        return this;
    }

    /**
     * 构建 eqIfPresent 的 Criteria
     * 仅用于 Criteria 的 or 操作
     */
    public Criteria eqIfPresentCriteria(SFunctionMongo<T, ?> column, Object val) {
        if (ObjectUtil.isNotEmpty(val)) {
            return Criteria.where(extractFieldName(column)).is(val);
        }
        return null;
    }

    /**
     * @param criteriaList Criteria 集合
     * 仅用于 Criteria 的 or 操作
     */
    public LambdaQueryWrapperMongo<T> or(Collection<Criteria> criteriaList) {
        Assert.notNull(criteriaList, "val must not be null");
        this.addCriteria(new Criteria().orOperator(criteriaList));
        return this;
    }
}
