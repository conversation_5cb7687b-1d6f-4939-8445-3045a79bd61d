package net.iofun.mdis.framework.dict.config;

import net.iofun.mdis.framework.common.biz.system.dict.DictDataCommonApi;
import net.iofun.mdis.framework.dict.core.DictFrameworkUtils;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Bean;

@AutoConfiguration
public class MdisDictAutoConfiguration {

    @Bean
//    @ConditionalOnBean(DictDataApi.class)
    @SuppressWarnings("InstantiationOfUtilityClass")
    public DictFrameworkUtils dictUtils(DictDataCommonApi dictDataApi) {
        DictFrameworkUtils.init(dictDataApi);
        return new DictFrameworkUtils();
    }

}
