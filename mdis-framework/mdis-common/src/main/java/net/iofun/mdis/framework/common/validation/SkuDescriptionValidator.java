package net.iofun.mdis.framework.common.validation;

import cn.hutool.core.text.CharSequenceUtil;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import net.iofun.mdis.framework.common.util.validation.ValidationUtils;

/**
 * <AUTHOR>
 */
public class SkuDescriptionValidator implements ConstraintValidator<SkuDescription, String> {
    @Override
    public void initialize(SkuDescription annotation) {
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        // 如果为空，默认校验不通过
        if (CharSequenceUtil.isEmpty(value)) {
            return true;
        }
        // 校验SKU description
        return ValidationUtils.isSkuDescription(value);
    }
}
