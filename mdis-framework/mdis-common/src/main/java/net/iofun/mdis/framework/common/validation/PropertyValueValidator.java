package net.iofun.mdis.framework.common.validation;

import cn.hutool.core.text.CharSequenceUtil;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import net.iofun.mdis.framework.common.util.validation.ValidationUtils;

/**
 * <AUTHOR>
 */
public class PropertyValueValidator implements ConstraintValidator<PropertyValue, String> {
    @Override
    public void initialize(PropertyValue annotation) {
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        // 如果为空，默认校验不通过
        if (CharSequenceUtil.isEmpty(value)) {
            return false;
        }
        // 校验sku 属性值
        return ValidationUtils.isPropertyValue(value);
    }
}
