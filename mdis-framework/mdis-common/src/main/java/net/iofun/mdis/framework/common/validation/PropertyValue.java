package net.iofun.mdis.framework.common.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 */
@Target({
        ElementType.METHOD,
        ElementType.FIELD,
        ElementType.ANNOTATION_TYPE,
        ElementType.CONSTRUCTOR,
        ElementType.PARAMETER,
        ElementType.TYPE_USE
})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(
        validatedBy = PropertyValueValidator.class
)
public @interface PropertyValue {
    String message() default "属性值格式不正确";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
