package net.iofun.mdis.framework.idempotent.config;

import net.iofun.mdis.framework.idempotent.core.aop.IdempotentAspect;
import net.iofun.mdis.framework.idempotent.core.keyresolver.impl.DefaultIdempotentKeyResolver;
import net.iofun.mdis.framework.idempotent.core.keyresolver.impl.ExpressionIdempotentKeyResolver;
import net.iofun.mdis.framework.idempotent.core.keyresolver.IdempotentKeyResolver;
import net.iofun.mdis.framework.idempotent.core.keyresolver.impl.UserIdempotentKeyResolver;
import net.iofun.mdis.framework.idempotent.core.redis.IdempotentRedisDAO;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import net.iofun.mdis.framework.redis.config.MdisRedisAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.List;

@AutoConfiguration(after = MdisRedisAutoConfiguration.class)
public class MdisIdempotentConfiguration {

    @Bean
    public IdempotentAspect idempotentAspect(List<IdempotentKeyResolver> keyResolvers, IdempotentRedisDAO idempotentRedisDAO) {
        return new IdempotentAspect(keyResolvers, idempotentRedisDAO);
    }

    @Bean
    public IdempotentRedisDAO idempotentRedisDAO(StringRedisTemplate stringRedisTemplate) {
        return new IdempotentRedisDAO(stringRedisTemplate);
    }

    // ========== 各种 IdempotentKeyResolver Bean ==========

    @Bean
    public DefaultIdempotentKeyResolver defaultIdempotentKeyResolver() {
        return new DefaultIdempotentKeyResolver();
    }

    @Bean
    public UserIdempotentKeyResolver userIdempotentKeyResolver() {
        return new UserIdempotentKeyResolver();
    }

    @Bean
    public ExpressionIdempotentKeyResolver expressionIdempotentKeyResolver() {
        return new ExpressionIdempotentKeyResolver();
    }

}
