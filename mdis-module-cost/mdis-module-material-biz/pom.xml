<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>net.iofun</groupId>
        <artifactId>mdis-module-cost</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>mdis-module-material-biz</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        材料采购管理模块, 包含采购流程, 招标, 订单, 交付管理;
        这里是材料采购管理模块的biz模块, 用于实现业务逻辑.
    </description>

    <dependencies>
        <dependency>
            <groupId>net.iofun</groupId>
            <artifactId>mdis-module-system</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>net.iofun</groupId>
            <artifactId>mdis-module-cost-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>net.iofun</groupId>
            <artifactId>mdis-module-material-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>net.iofun</groupId>
            <artifactId>mdis-module-ai-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>net.iofun</groupId>
            <artifactId>mdis-spring-boot-starter-datapermission</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>net.iofun</groupId>
            <artifactId>mdis-spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>net.iofun</groupId>
            <artifactId>mdis-spring-boot-starter-security</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>net.iofun</groupId>
            <artifactId>mdis-spring-boot-starter-mongo</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>net.iofun</groupId>
            <artifactId>mdis-spring-boot-starter-test</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>net.iofun</groupId>
            <artifactId>mdis-spring-boot-starter-excel</artifactId>
        </dependency>

    </dependencies>

</project>